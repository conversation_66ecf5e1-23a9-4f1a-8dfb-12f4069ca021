/* Modern Design System CSS */

/* CSS Variables for Modern Design */
:root {
    /* Primary Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    /* Secondary Colors */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;
    
    /* Success Colors */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;
    
    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;
    
    /* Danger Colors */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-300: #fca5a5;
    --danger-400: #f87171;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    --danger-800: #991b1b;
    --danger-900: #7f1d1d;
    
    /* Neutral Colors */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --primary-50: #1e3a8a;
    --primary-100: #1e40af;
    --primary-200: #1d4ed8;
    --primary-300: #2563eb;
    --primary-400: #3b82f6;
    --primary-500: #60a5fa;
    --primary-600: #93c5fd;
    --primary-700: #bfdbfe;
    --primary-800: #dbeafe;
    --primary-900: #eff6ff;
    
    --secondary-50: #0f172a;
    --secondary-100: #1e293b;
    --secondary-200: #334155;
    --secondary-300: #475569;
    --secondary-400: #64748b;
    --secondary-500: #94a3b8;
    --secondary-600: #cbd5e1;
    --secondary-700: #e2e8f0;
    --secondary-800: #f1f5f9;
    --secondary-900: #f8fafc;
    
    --neutral-50: #171717;
    --neutral-100: #262626;
    --neutral-200: #404040;
    --neutral-300: #525252;
    --neutral-400: #737373;
    --neutral-500: #a3a3a3;
    --neutral-600: #d4d4d4;
    --neutral-700: #e5e5e5;
    --neutral-800: #f5f5f5;
    --neutral-900: #fafafa;
}

/* Modern Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', 'Roboto', ui-sans-serif, system-ui, sans-serif;
    line-height: var(--line-height-normal);
    color: var(--secondary-800);
    background-color: var(--neutral-50);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Modern Topbar Styles */
.modern-topbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--neutral-200);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.modern-menu-toggle {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
}

.modern-menu-toggle:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
    transform: scale(1.05);
}

/* Search Bar Styles */
.search-container {
    margin-left: var(--spacing-lg);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--secondary-400);
    width: 18px;
    height: 18px;
    z-index: 1;
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-xl);
    background: var(--neutral-50);
    color: var(--secondary-700);
    font-size: var(--font-size-sm);
    width: 300px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    background: white;
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
    transform: scale(1.02);
}

/* Theme Toggle Button */
.theme-toggle-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
    position: relative;
}

.theme-toggle-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
    transform: scale(1.05);
}

/* Fullscreen Button */
.fullscreen-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
}

.fullscreen-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
    transform: scale(1.05);
}

/* Notification Button */
.notification-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    position: relative;
    transition: all var(--transition-fast);
}

.notification-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
    transform: scale(1.05);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Notifications Dropdown */
.notifications-dropdown {
    width: 380px;
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: var(--spacing-sm);
}

.notification-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--neutral-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-800);
    margin: 0;
}

.notification-clear-btn {
    color: var(--primary-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.notification-clear-btn:hover {
    color: var(--primary-700);
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    padding: var(--spacing-lg);
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid var(--neutral-100);
    transition: all var(--transition-fast);
}

.notification-item:hover {
    background: var(--neutral-50);
    transform: translateX(4px);
}

.notification-item.active {
    background: var(--primary-50);
    border-left: 3px solid var(--primary-500);
}

.notification-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.notification-content {
    margin-left: var(--spacing-md);
    flex: 1;
}

.notification-header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.notification-user {
    font-weight: 500;
    color: var(--secondary-800);
    margin: 0;
}

.notification-time {
    color: var(--secondary-500);
    font-size: var(--font-size-xs);
}

.notification-message {
    color: var(--secondary-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.notification-file {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xs);
}

.file-icon {
    color: var(--primary-600);
}

.file-name {
    font-weight: 500;
    color: var(--secondary-800);
    margin: 0;
}

.file-size {
    color: var(--secondary-500);
    font-size: var(--font-size-xs);
}

.notification-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--neutral-200);
    text-align: center;
}

.view-all-btn {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-fast);
}

.view-all-btn:hover {
    color: var(--primary-700);
    transform: translateX(4px);
}

/* User Profile Button */
.user-profile-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.user-profile-btn:hover {
    background: var(--neutral-100);
    transform: scale(1.02);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.user-name {
    font-weight: 500;
    color: var(--secondary-800);
}

.dropdown-icon {
    width: 16px;
    height: 16px;
    color: var(--secondary-500);
    transition: transform var(--transition-fast);
}

.user-profile-btn[aria-expanded="true"] .dropdown-icon {
    transform: rotate(180deg);
}

/* User Dropdown Menu */
.user-dropdown-menu {
    width: 280px;
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: var(--spacing-sm);
}

.profile-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.profile-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.profile-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.profile-email {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    text-decoration: none;
    color: var(--secondary-700);
    transition: all var(--transition-fast);
}

.profile-menu-item:hover {
    background: var(--neutral-50);
    color: var(--primary-600);
    transform: translateX(4px);
}

.profile-menu-item.logout-item {
    color: var(--danger-600);
}

.profile-menu-item.logout-item:hover {
    background: var(--danger-50);
    color: var(--danger-700);
}

.menu-icon {
    width: 18px;
    height: 18px;
}

/* Modern Sidebar Styles */
.app-sidebar-menu {
    background: white;
    border-right: 1px solid var(--neutral-200);
    transition: all var(--transition-normal);
}

.logo-box {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--neutral-200);
    text-align: center;
}

.logo {
    display: inline-block;
    transition: all var(--transition-fast);
}

.logo:hover {
    transform: scale(1.05);
}

.menu-title {
    color: var(--secondary-500);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-sm);
    margin: var(--spacing-lg) 0 var(--spacing-sm);
    border-bottom: 1px solid var(--neutral-100);
}

.tp-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    color: var(--secondary-700);
    text-decoration: none;
    border-radius: var(--radius-lg);
    margin: 0 var(--spacing-md);
    transition: all var(--transition-fast);
    position: relative;
}

.tp-link:hover {
    background: var(--primary-50);
    color: var(--primary-700);
    transform: translateX(4px);
}

.tp-link.active {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    box-shadow: var(--shadow-md);
}

.tp-link.active::before {
    content: '';
    position: absolute;
    left: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: var(--primary-600);
    border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.tp-link i {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-md);
    transition: all var(--transition-fast);
}

.tp-link:hover i {
    transform: scale(1.1);
}

.tp-link span {
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* Modern Card Styles */
.card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-200);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.card-body {
    padding: var(--spacing-xl);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-800);
    margin: 0;
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-tight);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    gap: var(--spacing-xs);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-100);
    color: var(--secondary-700);
    border: 1px solid var(--secondary-200);
}

.btn-secondary:hover {
    background: var(--secondary-200);
    color: var(--secondary-800);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-500), var(--danger-600));
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-600), var(--danger-700));
    transform: translateY(-1px);
}

/* Modern Form Styles */
.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--secondary-800);
    background-color: white;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
    transform: scale(1.02);
}

/* Modern Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--neutral-50);
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    font-weight: 600;
    color: var(--secondary-700);
    border-bottom: 1px solid var(--neutral-200);
    font-size: var(--font-size-sm);
}

.table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--neutral-100);
    color: var(--secondary-600);
    font-size: var(--font-size-sm);
}

.table tr:hover {
    background: var(--neutral-50);
}

.table tr:last-child td {
    border-bottom: none;
}

/* Modern Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--radius-md);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background: var(--primary-100);
    color: var(--primary-800);
}

.badge-success {
    background: var(--success-100);
    color: var(--success-800);
}

.badge-warning {
    background: var(--warning-100);
    color: var(--warning-800);
}

.badge-danger {
    background: var(--danger-100);
    color: var(--danger-800);
}

/* Dashboard Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--secondary-800);
    margin: var(--spacing-sm) 0;
}

.stats-label {
    color: var(--secondary-500);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.stats-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stats-change.positive {
    color: var(--success-600);
}

.stats-change.negative {
    color: var(--danger-600);
}

/* Content Page Styles */
.content-page {
    background: var(--neutral-50);
    min-height: 100vh;
    transition: background-color var(--transition-normal);
}

.page-header {
    background: white;
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--neutral-200);
    margin-bottom: var(--spacing-xl);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--secondary-800);
    margin: 0;
}

.page-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--secondary-500);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .search-input {
        width: 250px;
    }
}

@media (max-width: 992px) {
    .search-container {
        display: none !important;
    }

    .topbar-right {
        gap: var(--spacing-sm) !important;
    }
}

@media (max-width: 768px) {
    .user-info {
        display: none !important;
    }

    .theme-toggle-btn,
    .fullscreen-btn {
        display: none !important;
    }

    .notifications-dropdown,
    .user-dropdown-menu {
        width: 320px;
    }

    .card {
        margin-bottom: var(--spacing-lg);
    }

    .stats-card {
        margin-bottom: var(--spacing-md);
    }

    .table-responsive {
        border-radius: var(--radius-lg);
        overflow: hidden;
    }
}

@media (max-width: 576px) {
    .notifications-dropdown,
    .user-dropdown-menu {
        width: 280px;
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .stats-card {
        padding: var(--spacing-lg);
    }

    .page-title {
        font-size: var(--font-size-xl);
    }
}

/* Dark Mode Styles */
[data-theme="dark"] body {
    background-color: var(--neutral-100);
    color: var(--neutral-800);
}

[data-theme="dark"] .modern-topbar {
    background: rgba(30, 41, 59, 0.95);
    border-bottom-color: var(--neutral-300);
}

[data-theme="dark"] .app-sidebar-menu {
    background: var(--neutral-200);
    border-right-color: var(--neutral-300);
}

[data-theme="dark"] .card {
    background: var(--neutral-200);
    border-color: var(--neutral-300);
}

[data-theme="dark"] .stats-card {
    background: var(--neutral-200);
    border-color: var(--neutral-300);
}

[data-theme="dark"] .table {
    background: var(--neutral-200);
}

[data-theme="dark"] .table th {
    background: var(--neutral-300);
}

[data-theme="dark"] .content-page {
    background: var(--neutral-100);
}

[data-theme="dark"] .page-header {
    background: var(--neutral-200);
    border-bottom-color: var(--neutral-300);
}
