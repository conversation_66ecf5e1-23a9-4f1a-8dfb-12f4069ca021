
<?php $__env->startSection('title'); ?>
    Modifiez Client
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="content ">
        <!-- Start Content-->

        <div class="row py-3">
            <div class="col-md-3"></div>
            <!-- Grids Modal -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">

                                <a href="<?php echo e(route('client.index')); ?>"
                                    class="btn btn-warning btn-sm"><i data-feather="arrow-left"></i></a>

                            <div class="text-center">
                                <h4><strong>Modifiez Client</strong></h>
                            </div>                           

                    </div><!-- end card header -->

                    <div class="card-body">
                        <form id="myForm" action="<?php echo e(route('client.update', $client)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('put'); ?>
                            <div class="row g-3">
                                <div class="col-xxl-6">
                                    <label for="type_client" class="form-label">Type</label>
                                    <select name="type_client"
                                        class="form-control <?php $__errorArgs = ['type_client'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> select"
                                        id="type_client">
                                        <option value="<?php echo e($client->type_client); ?>"><?php echo e($client->type_client); ?></option>
                                        <option value="Particulier">Particulier</option>
                                        <option value="Professionnel">Professionnel</option>
                                    </select>
                                    <?php $__errorArgs = ['type_client'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div id="mf" class="col-xxl-6"></div>

                                <div class="col-xxl-6">
                                    <div>
                                        <label for="name" class="form-label">Nom</label>
                                        <input type="text" name="name"
                                            class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name"
                                            placeholder="Entrer le nom" value="<?php echo e(old('name', $client->name)); ?>">
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                </div><!--end col-->

                                <div class="col-xxl-6">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" name="email"
                                        class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email"
                                        placeholder="Entrer l'email" value="<?php echo e(old('email', $client->email)); ?>">
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div><!--end col-->

                                <div class="col-xxl-6">
                                    <label for="phone" class="form-label">Téléphone</label>
                                    <input type="text" name="phone"
                                        class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone"
                                        placeholder="Entrer le téléphone" value="<?php echo e(old('phone', $client->phone)); ?>">
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div><!--end col-->

                                <div class="col-xxl-6">
                                    <div>
                                        <label for="address" class="form-label">Adresse</label>
                                        <textarea type="text" name="address" class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="address"
                                            placeholder="Entrer l'adredde"><?php echo e(old('address', $client->address)); ?></textarea>
                                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                    </div>

                                </div><!--end col-->
                                <div class="col-xxl-6">
                                    <div>
                                        <label for="sender" class="form-label">Envoyeur</label>
                                        <input type="text" name="sender"
                                            class="form-control <?php $__errorArgs = ['sender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sender"
                                            placeholder="Entrer l'envoyeur" value="<?php echo e(old('sender', $client->sender)); ?>">
                                        <?php $__errorArgs = ['sender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="hstack gap-2 justify-content-center">
                                        <button type="submit" class="btn btn-primary">Modifiez</button>
                                    </div>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </form> <!-- end form -->
                    </div><!-- end card-body -->
                </div><!-- end card -->
            </div> <!--end col-->
        </div> <!-- end row -->

    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const html = `
            <label for="name" class="form-label">Matricule Fiscal</label>
            <input type="text" name="mf" class="form-control <?php $__errorArgs = ['mf'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="mf"
                placeholder="Entrer matricule fiscal" value="<?php echo e(old('mf', $client->mf)); ?>">
            <?php $__errorArgs = ['mf'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-danger"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>`;
            const select = document.querySelector('.select');
            const mf = document.getElementById('mf');
            const back = document.getElementById('type_client').options[select.selectedIndex].textContent;
            if (back === 'Professionnel') {
                mf.innerHTML = html;
            } else {
                mf.innerHTML = '';
            }
            select.addEventListener('change', function() {
                if (this.options[this.selectedIndex].textContent === 'Professionnel') {
                    mf.innerHTML = html;
                } else {
                    mf.innerHTML = '';
                }
            })
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\client\edit.blade.php ENDPATH**/ ?>