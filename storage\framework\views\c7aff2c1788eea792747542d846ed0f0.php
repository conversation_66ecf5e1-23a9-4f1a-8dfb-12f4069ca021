
<?php $__env->startSection('title'); ?>
    Client
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="content">

        <!-- Start Content-->
        <div class="container-xxl">

            <!-- Datatables  -->
            <div class="row py-3">
                <div class="col-12">
                    <div class="card">

                        <div class="card-header">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title mb-0">Liste des clients</h5>
                                </div>
                                <div class="col">
                                    <a href="<?php echo e(route('client.create')); ?>" class="btn btn-primary float-end">Ajouter</a>
                                </div>
                            </div>

                        </div><!-- end card header -->

                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="table1" class="table table-striped table-traffic mb-0">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Téléphone</th>
                                            <th>Email</th>
                                            <th>Adresse</th>
                                            <th>Envoyeur</th>
                                            <th>Créer Par</th>
                                            <th>Nombre des Véhicules</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__empty_1 = true; $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                            <tr>
                                                <td><?php echo e($client->name); ?></td>
                                                <td><?php echo e($client->phone); ?></td>
                                                <td><?php echo e($client->email); ?></td>
                                                <td><?php echo e($client->address); ?></td>
                                                <td><?php echo e($client->sender); ?></td>
                                                <td>
                                                    <?php
                                                        $user = App\Models\User::findOrFail($client->created_by);
                                                    ?>
                                                    <?php echo e($user->name); ?>

                                                </td>
                                                <td class="text-center">
                                                    <span class="car-count" data-client-id="<?php echo e($client->id); ?>">
                                                        <span style="cursor: pointer;"
                                                            class="car-number rounded-pill bg-primary text-white px-2 py-1">0</span></span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">

                                                        
                                                        <a href="<?php echo e(route('client.show', $client)); ?>"
                                                            class="btn btn-outline-info fs-18 rounded-2 m-0 p-0">
                                                            <i class="mdi mdi-eye fs-18 rounded-2 p-1"></i>
                                                        </a>

                                                        
                                                        <a href="<?php echo e(route('client.edit', $client)); ?>"
                                                            class="btn btn-outline-warning fs-18 rounded-2 m-0 p-0">
                                                            <i class="mdi mdi-pencil fs-18 rounded-2 p-1"></i>
                                                        </a>

                                                        
                                                        <form action="<?php echo e(route('client.destroy', $client)); ?>"
                                                            method="POST"
                                                            onsubmit="return confirm('Confirmer la suppression ?')"
                                                            style="display:inline;">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit"
                                                                class="btn btn-outline-danger fs-18 rounded-2 m-0 p-0">
                                                                <i class="mdi mdi-delete fs-18 rounded-2 p-1"></i>
                                                            </button>
                                                        </form>

                                                    </div>
                                                </td>

                                            </tr>

                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                            <td colspan="8" class="text-md-center text-danger"> Pas de donnée a afficher
                                            </td>
                                        <?php endif; ?>

                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            <!-- Button Datatable -->


        </div> <!-- container-fluid -->

    </div> <!-- content -->

    <div id="car-popup"
        style="position: absolute; display: none; background: #fff; border: 1px solid #ccc; box-shadow: 0 4px 8px rgba(0,0,0,0.1); z-index: 9999; border-radius: 4px;">
    </div>

    <script>
        const allCars = <?php echo json_encode($cars, 15, 512) ?>;

        document.addEventListener('DOMContentLoaded', () => {
            const popup = document.getElementById('car-popup');
            const carCountElements = document.querySelectorAll('.car-count');
            let hideTimeout;

            carCountElements.forEach(element => {
                const clientId = parseInt(element.dataset.clientId);
                const clientCars = allCars.filter(car => car.client_id === clientId);

                // عرض عدد السيارات
                element.querySelector('.car-number').innerText = clientCars.length;

                // عند تمرير الماوس على العنصر
                element.addEventListener('mouseenter', () => {
                    if (!clientCars.length) return;

                    const showRoute = "<?php echo e(route('car.show', ':id')); ?>";
                    const listItems = clientCars.map(car => {
                        const url = showRoute.replace(':id', car.id);
                        return `
                        <li>
                            <a href="${url}" class="car-link"
                               style="text-decoration: none; color: #333; display: block; padding: 7px 8px;">
                                ${car.marque} - ${car.model} - ${car.immatriculation}
                            </a>
                        </li>`;
                    }).join('');

                    popup.innerHTML =
                        `<ul style="margin:0;padding:0;list-style:none;">${listItems}</ul>`;

                    const rect = element.getBoundingClientRect();
                    popup.style.top = `${window.scrollY + rect.top}px`;
                    popup.style.left = `${window.scrollX + rect.right + 5}px`;
                    popup.style.display = 'block';
                });

                // عند مغادرة العنصر
                element.addEventListener('mouseleave', () => {
                    hideTimeout = setTimeout(() => popup.style.display = 'none', 100);
                });
            });

            // التحكم بالبقاء عند مرور الماوس على الـ popup نفسه
            popup.addEventListener('mouseenter', () => {
                clearTimeout(hideTimeout);
            });

            popup.addEventListener('mouseleave', () => {
                popup.style.display = 'none';
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views/client/index.blade.php ENDPATH**/ ?>