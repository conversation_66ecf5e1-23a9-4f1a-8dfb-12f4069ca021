
<?php $__env->startSection('title'); ?>
    Rendez-vous
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <style>
        .album-trigger {
            cursor: pointer;
            background: linear-gradient(135deg, #007bff, #6610f2);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .album-trigger:hover {
            background: linear-gradient(135deg, #0056b3, #520dc2);
        }

        .album-container {
            margin-top: 2rem;
            display: none;
        }

        .image-wrapper {
            position: relative;
            aspect-ratio: 1 / 1;
            overflow: hidden;
            border-radius: 10px;
            background: #f8f9fa;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .album-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            display: block;
        }

        .album-image:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .spinner-overlay {
            position: absolute;
            inset: 0;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            font-weight: bold;
            color: #007bff;
            font-size: 1rem;
            z-index: 10;
        }

        .viewer-overlay {
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .viewer-overlay img {
            max-width: 90vw;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 10px;
        }

        .viewer-close,
        .viewer-arrow {
            position: absolute;
            font-size: 2.5rem;
            color: white;
            cursor: pointer;
            z-index: 10000;
        }

        .viewer-close {
            top: 20px;
            right: 30px;
        }

        .viewer-arrow.left {
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
        }

        .viewer-arrow.right {
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
    <div class="container-xxl py-4">
        <div class="card shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <a href="<?php echo e(url()->previous()); ?>" class="btn btn-outline-info btn-sm">
                    <i class="mdi mdi-arrow-left"></i> Retour
                </a>
                <div class="text-center">
                    <h5 class="mb-0">
                        <span class="badge bg-info text-dark"><?php echo e($rdv->car->marque); ?> <?php echo e($rdv->car->model); ?></span>
                        <span class="badge bg-secondary"><?php echo e($rdv->car->immatriculation); ?></span>
                    </h5>
                </div>
            </div>
            <div class="card-body">
                <h5 class="card-title mb-3">🗓️ Détails du rendez-vous</h5>
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="fw-bold text-muted">Date</div>
                            <div class="fs-5"><?php echo e(\Carbon\Carbon::parse($rdv->date)->format('d/m/Y')); ?></div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="fw-bold text-muted">Heure</div>
                            <div class="fs-5"><?php echo e(\Carbon\Carbon::parse($rdv->time)->format('H:i')); ?></div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="fw-bold text-muted">Statut</div>
                            <?php
                                $statusColors = [
                                    'Immediat' => 'success',
                                    'Fixé' => 'primary',
                                    'En attente' => 'warning',
                                ];
                                $color = $statusColors[$rdv->status] ?? 'danger';
                            ?>
                            <span class="badge bg-<?php echo e($color); ?> fs-6"><?php echo e($rdv->status); ?></span>
                        </div>
                    </div>
                </div>
                <hr>
                <?php if($rdv->status == 'En attente'): ?>
                    <form method="POST" action="<?php echo e(route('rdv.uploads', $rdv->id)); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row g-3">
                            <div class="col-md-5">
                                <label class="form-label fw-bold">📷 Photos</label>
                                <input type="file" name="photoAv[]" class="form-control" accept="image/*" multiple>
                            </div>
                            <div hidden class="col-md-5">
                                <input type="file" name="photoAp[]" class="form-control" accept="image/*" multiple>
                            </div>
                            <div class="col-md-5">
                                <label class="form-label fw-bold">📁 Fichiers</label>
                                <input type="file" name="files[]" class="form-control"
                                    accept=".pdf,.doc,.xls,.csv,.odt,.ods" multiple>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">⬆️ Charger</button>
                            </div>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="row">
                        <!-- عمود الأيقونات -->
                        <div class="col-md-4 col-lg-3 mb-3">
                            <div class="d-flex flex-column gap-3">
                                <?php $__currentLoopData = [
            'photoAv' => ['id' => 'albumAvant', 'label' => 'Photo Avant', 'image' => $rdv->photoAv->first()->image_path ?? null],
            'photoAp' => ['id' => 'albumApres', 'label' => 'Photo Après', 'image' => 'assets/images/gallery/imgap.png'],
            'files' => ['id' => 'fichiers', 'label' => 'Fichier Lié', 'image' => 'assets/images/gallery/fichier.png'],
        ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($rdv->$key->count() > 0): ?>
                                        <div id="<?php echo e($item['id']); ?>" class="cursor-pointer">
                                            <div class="p-3 border rounded text-white position-relative overflow-hidden"
                                                style="aspect-ratio: 1/1; background: url('<?php echo e(url($item['image'])); ?>') center center / cover no-repeat;">
                                                <div class="position-absolute top-50 start-50 translate-middle text-center">
                                                    <h6 class="fw-bold"><?php echo e($item['label']); ?></h6>
                                                </div>
                                                <div class="position-absolute top-0 start-0 w-100 h-100"
                                                    style="background: rgba(0, 0, 0, 0.4);"></div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>

                        <!-- عمود المحتوى -->
                        <div class="col-md-8 col-lg-9">
                            <div id="left" class="bg-light border rounded p-3" style="height: 75vh; overflow-y: auto;">
                                <h4 class="text-muted text-center my-5">Aucune tâche assignée</h4>
                            </div>
                        </div>

                    </div>

                    <div id="image-viewer" class="viewer-overlay" style="display: none;">
                        <div class="viewer-close" onclick="closeViewer()">✕</div>
                        <div class="viewer-arrow left" onclick="prevImage()">❮</div>
                        <img id="viewer-image" src="">
                        <div class="viewer-arrow right" onclick="nextImage()">❯</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <script>
        const left = document.getElementById('left');
        const albumAvant = document.getElementById('albumAvant');
        const images = <?php echo json_encode($rdv->photoAv->map(fn($item) => url($item->image_path))->toArray(), 15, 512) ?>;
        let currentIndex = 0,
            autoSlideInterval;
        const viewer = document.getElementById('image-viewer');
        const viewerImage = document.getElementById('viewer-image');

        if (albumAvant) {
            albumAvant.addEventListener('click', () => {
                left.innerHTML = '<div class="image-grid row row-cols-2 row-cols-md-3 g-3"></div>';
                const grid = left.querySelector('.image-grid');

                images.forEach((src, index) => {
                    const col = document.createElement('div');
                    col.className = 'col';

                    const wrapper = document.createElement('div');
                    wrapper.className = 'image-wrapper';

                    const img = document.createElement('img');
                    img.className = 'album-image';
                    img.dataset.index = index;

                    const spinner = document.createElement('div');
                    spinner.className = 'spinner-overlay';
                    spinner.innerHTML =
                        `<div class="spinner-border text-primary mb-2" role="status"></div><div class="progress-text">0%</div>`;

                    wrapper.appendChild(img);
                    wrapper.appendChild(spinner);
                    col.appendChild(wrapper);

                    // ✅ هذا هو التعديل الرئيسي
                    grid.appendChild(col);

                    const xhr = new XMLHttpRequest();
                    xhr.open("GET", src, true);
                    xhr.responseType = "blob";
                    xhr.onprogress = function(e) {
                        if (e.lengthComputable) {
                            spinner.querySelector('.progress-text').innerText = Math.round((e.loaded / e
                                .total) * 100) + '%';
                        }
                    };
                    xhr.onload = function() {
                        const blobUrl = URL.createObjectURL(xhr.response);
                        img.src = blobUrl;
                        img.onload = () => {
                            spinner.remove();
                            img.style.display = 'block';
                        };
                    };
                    xhr.send();
                });


                setTimeout(() => {
                    document.querySelectorAll('.album-image').forEach(img => {
                        img.addEventListener('click', () => {
                            currentIndex = parseInt(img.dataset.index);
                            showImage(currentIndex);
                        });
                    });
                }, 100);
            });
        }

        function showImage(index) {
            viewerImage.src = images[index];
            viewer.style.display = 'flex';
            startAutoSlide();
        }

        function closeViewer() {
            viewer.style.display = 'none';
            stopAutoSlide();
        }

        function nextImage() {
            currentIndex = (currentIndex + 1) % images.length;
            showImage(currentIndex);
        }

        function prevImage() {
            currentIndex = (currentIndex - 1 + images.length) % images.length;
            showImage(currentIndex);
        }

        function startAutoSlide() {
            stopAutoSlide();
            autoSlideInterval = setInterval(() => nextImage(), 3000);
        }

        function stopAutoSlide() {
            if (autoSlideInterval) clearInterval(autoSlideInterval);
        }

        viewer.addEventListener('wheel', e => {
            e.preventDefault();
            e.deltaY < 0 ? prevImage() : nextImage();
        });

        let touchStartX = 0;
        viewer.addEventListener('touchstart', e => touchStartX = e.changedTouches[0].screenX);
        viewer.addEventListener('touchend', e => {
            let touchEndX = e.changedTouches[0].screenX;
            if (touchEndX < touchStartX - 50) nextImage();
            else if (touchEndX > touchStartX + 50) prevImage();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\rdv\show.blade.php ENDPATH**/ ?>