<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PhotoAp extends Model
{
    use HasFactory;

    protected $table = 'photo_aps';

    protected $fillable = [
        'rdv_id',
        'image_path',
    ];

    public function rdv()
    {
        return $this->belongsTo(Rdv::class);
    }

    public function car()
    {
        return $this->belongsTo(Car::class);
    }
}