
<?php $__env->startSection('title'); ?>
 Véhicules Clients
 <?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="content">

        <!-- Start Content-->
        <div class="container-xxl">

            <!-- Datatables  -->
            <div class="row py-3">
                <div class="col-12">
                    <div class="card">

                        <div class="card-header">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title mb-0">Liste des véhicules clients</h5>
                                </div>
                                <div class="col">
                                    <a href="<?php echo e(route('car.create')); ?>" class="btn btn-primary float-end">Ajouter</a>
                                </div>
                            </div>

                        </div><!-- end card header -->

                         <div class="card-body">
                            <div class="table-responsive">
                            <table id="table1" class="table table-striped table-traffic mb-0">
                                <thead>
                                    <tr>
                                        <th>Marque</th>
                                        <th>Modele</th>
                                        <th>Immatriculation</th>
                                        <th>N° Chassis</th>
                                        <th>Date Mise en circulation</th>
                                        <th>Client</th>
                                        <th>Envoyeur</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $cars; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $car): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                     <tr>
                                        <td><?php echo e($car->marque); ?></td>                                       
                                        <td><?php echo e($car->model); ?></td>
                                        <td><?php echo e($car->immatriculation); ?></td>
                                        <td><?php echo e($car->n_chassis); ?></td>
                                        <td><?php echo e($car->dpmc); ?></td>
                                        <td><?php echo e($car->client->name); ?></td>                                      
                                        <td><?php echo e($car->client->sender); ?></td>                                      
                                        <td>
                                            
                                                <a href="<?php echo e(route('car.show', $car)); ?>" ><i class="mdi mdi-eye text-info fs-18 rounded-2 border p-1"></i></a>
                                                <a href="<?php echo e(route('car.edit', $car)); ?>" ><i class="mdi mdi-pencil text-warning fs-18 rounded-2 border p-1 me-1"></i></a>                                           
                                        </td>
                                    </tr>
                                        
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                       <td colspan="8" class="text-md-center text-danger"> Pas de donnée a afficher </td> 
                                    <?php endif; ?>
                                                                          
                                </tbody>
                            </table>
                            </div>
                        </div>

                    </div>
                </div>
            </div>


            <!-- Button Datatable -->


        </div> <!-- container-fluid -->

    </div> <!-- content -->


    <!-- end Footer -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\car\index.blade.php ENDPATH**/ ?>