"use strict";

// الشارت الأول
var options = {
  series: [{ data: [40, 55, 40, 60, 48, 28, 56, 60] }],
  chart: {
    height: 45,
    type: "bar",
    sparkline: { enabled: !0 },
    animations: { enabled: !1 }
  },
  colors: ["#537AEF"],
  plotOptions: { bar: { columnWidth: "40%", borderRadius: 2 } },
  dataLabels: { enabled: !1 },
  fill: { opacity: 1 },
  grid: { strokeDashArray: 4 },
  labels: [1, 2, 3, 4, 5, 6, 7, 8],
  xaxis: { crosshairs: { width: 1 } },
  yaxis: { labels: { padding: 4 } },
  tooltip: { theme: "light" },
  legend: { show: !1 }
};
var chart = new ApexCharts(document.querySelector("#new-orders"), options);
chart.render();

// الشارت الثاني
options = {
  series: [{ data: [55, 25, 35, 55, 35, 65, 40, 65] }],
  chart: {
    height: 45,
    type: "bar",
    sparkline: { enabled: !0 },
    animations: { enabled: !1 }
  },
  colors: ["#ec8290"],
  plotOptions: { bar: { columnWidth: "40%", borderRadius: 2 } },
  dataLabels: { enabled: !1 },
  fill: { opacity: 1 },
  grid: { strokeDashArray: 4 },
  labels: [1, 2, 3, 4, 5, 6, 7, 8],
  xaxis: { crosshairs: { width: 1 } },
  yaxis: { labels: { padding: 4 } },
  tooltip: { theme: "light" },
  legend: { show: !1 }
};
chart = new ApexCharts(document.querySelector("#sales-report"), options);
chart.render();

// الشارت الثالث
options = {
  series: [{ data: [60, 38, 30, 50, 42, 37, 44, 60] }],
  chart: {
    height: 45,
    type: "bar",
    sparkline: { enabled: !0 },
    animations: { enabled: !1 }
  },
  colors: ["#29aa85"],
  plotOptions: { bar: { columnWidth: "40%", borderRadius: 2 } },
  dataLabels: { enabled: !1 },
  fill: { opacity: 1 },
  grid: { strokeDashArray: 4 },
  labels: [1, 2, 3, 4, 5, 6, 7, 8],
  xaxis: { crosshairs: { width: 1 } },
  yaxis: { labels: { padding: 4 } },
  tooltip: { theme: "light" },
  legend: { show: !1 }
};
chart = new ApexCharts(document.querySelector("#revenue"), options);
chart.render();

// الشارت الرابع
options = {
  series: [{ data: [65, 38, 28, 55, 40, 35, 50, 70] }],
  chart: {
    height: 45,
    type: "bar",
    sparkline: { enabled: !0 },
    animations: { enabled: !1 }
  },
  colors: ["#537AEF"],
  plotOptions: { bar: { columnWidth: "40%", borderRadius: 2 } },
  dataLabels: { enabled: !1 },
  fill: { opacity: 1 },
  grid: { strokeDashArray: 4 },
  labels: [1, 2, 3, 4, 5, 6, 7, 8],
  xaxis: { crosshairs: { width: 1 } },
  yaxis: { labels: { padding: 4 } },
  tooltip: { theme: "light" },
  legend: { show: !1 }
};
chart = new ApexCharts(document.querySelector("#expenses"), options);
chart.render();

// شارت المال (مع شهور فرنسية)
options = {
  chart: {
    type: "bar",
    height: 333,
    stacked: !0,
    parentHeightOffset: 0,
    toolbar: { show: !1 },
    zoom: { enabled: !0 }
  },
  series: [
    { name: "Profit", data: [300, 333, 258, 295, 258, 326, 275, 283, 271, 316, 334, 271] },
    { name: "Revenu", data: [300, 333, 258, 295, 258, 326, 275, 283, 271, 316, 333, 271] },
    { name: "Dépense", data: [300, 333, 258, 295, 259, 326, 275, 283, 271, 316, 333, 271] }
  ],
  plotOptions: {
    bar: {
      horizontal: !1,
      borderRadius: 5,
      borderRadiusApplication: "end",
      borderRadiusWhenStacked: "last",
      columnWidth: "40%",
      dataLabels: {
        total: {
          style: { fontSize: "13px", fontWeight: 900 }
        }
      }
    }
  },
  dataLabels: { enabled: !1 },
  xaxis: {
    categories: ["Janv", "Févr", "Mars", "Avr", "Mai", "Juin", "Juil", "Août", "Sept", "Oct", "Nov", "Déc"],
    axisBorder: { show: !1 }
  },
  yaxis: { labels: { padding: 4 } },
  colors: ["#1a4de7", "#537AEF", "#dee2e6"],
  legend: {
    position: "top",
    show: !0,
    horizontalAlign: "right"
  },
  fill: { opacity: 1 },
  grid: {
    padding: { top: -20, right: 0, bottom: 0 },
    strokeDashArray: 4,
    xaxis: { lines: { show: !0 } }
  }
};
chart = new ApexCharts(document.querySelector("#chart-money"), options);
chart.render();

// شارت المبيعات حسب الدولة
options = {
  series: [
    { name: "Tunisie", data: [80, 50, 30, 40, 100, 20] },
    { name: "Algérie", data: [20, 30, 40, 80, 20, 80] },
    { name: "Lybie", data: [44, 76, 78, 13, 43, 10] }
  ],
  chart: {
    type: "radar",
    height: 333,
    parentHeightOffset: 0,
    dropShadow: {
      enabled: !0,
      blur: 1,
      left: 1,
      top: 1
    },
    toolbar: { show: !1 }
  },
  stroke: { width: 2, dashArray: 2 },
  fill: { opacity: 0.1 },
  markers: { size: 0, hover: { size: 10 } },
  yaxis: { stepSize: 20 },
  xaxis: {
    categories: ["2020", "2021", "2022", "2023", "2024","2025"],
    labels: {
      show: !0,
      style: {
        colors: Array(6).fill("#001b2f"),
        fontSize: "13px"
      }
    }
  },
  colors: ["#ec8290", "#537AEF", "#8c57d1"],
  dataLabels: {
    enabled: !1,
    background: { enabled: !0 }
  }
};
chart = new ApexCharts(document.querySelector("#sales-country"), options);
chart.render();

// شارت العملاء الجدد والقدامى
options = {
  series: [
    { name: "Nouveau Client", data: [85, 80, 150, 127, 220, 200, 300, 290, 314] },
    { name: "Client Fidèle", data: [215, 165, 100, 200, 145, 185, 104, 124, 82] }
  ],
  chart: {
    type: "line",
    height: 305,
    parentHeightOffset: 0,
    zoom: { enabled: !1 },
    toolbar: { show: !1 },
    animations: { enabled: !1 }
  },
  dataLabels: { enabled: !1 },
  fill: { opacity: 1 },
  stroke: {
    width: [2, 2],
    curve: "straight",
    dashArray: [0, 7]
  },
  legend: {
    position: "top",
    horizontalAlign: "right",
    tooltipHoverFormatter: function (seriesName, opts) {
      return "Série : " + seriesName + " <strong>" + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + "</strong>";
    }
  },
  markers: { size: 0, hover: { sizeOffset: 6 } },
  grid: {
    strokeDashArray: 4,
    xaxis: { lines: { show: !0 } }
  },
  xaxis: {
    labels: { padding: 0 },
    axisBorder: { show: !1 },
    tooltip: { enabled: !1 },
    categories: ["09", "10", "11", "12", "13", "14", "15", "16"]
  },
  tooltip: {
    y: [
      {
        title: {
          formatter: function (val) {
            return "Valeur";
          }
        }
      },
      {
        title: {
          formatter: function (val) {
            return "Valeur";
          }
        }
      }
    ]
  },
  colors: ["#537AEF", "#5be7bd"]
};
chart = new ApexCharts(document.querySelector("#repeat-customer"), options);
chart.render();
