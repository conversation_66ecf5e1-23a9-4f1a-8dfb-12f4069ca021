<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Car;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use function Laravel\Prompts\error;

class ClientController extends Controller
{
   
    public function index()
    {
        $clients = Client::all();
        $cars = Car::all();
        return view( 'client.index', compact('clients', 'cars') );
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('client.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        if ($request->type_client == 'Professionnel') {
           
            $data = $request->validate([
            'name' => 'required|string',                  
            'email' => 'required|email|unique:clients,email',
            'phone' => 'required|digits:8|unique:clients,phone',
            'mf' => 'required|string|unique:clients,mf',
            'address' => 'required|string',
            'sender' => 'required|string',
            'type_client' => 'required|in:Particulier,Professionnel',
    ], [
                'required' => 'Le champ est obligatoire',
                'digits' => 'Le numero de téléphone (8 chiffres) ',
                'mf.unique' => 'Matricule fiscal existe déja',
                'email.unique' => 'L\'email existe déja',
                'phone.unique' => 'Le Téléphone existe déja'
            ]
        );
        } else {
            $data = $request->validate([           
            'name' => 'required|string',
            'email' => 'required|email|unique:clients,email',
            'phone' => 'required|digits:8|unique:clients,phone',
            'address' => 'required|string',
            'sender' => 'required|string',
            'type_client' => 'required|in:Particulier,Professionnel',
    ], [
                'required' => 'Le champ est obligatoire',
                'digits' => 'Vous dever sezire un numero de 8 chiffres',
                'email.unique' => 'L\'email existe déja',
                'phone.unique' => 'Le Téléphone existe déja'
            ]
        );
        $data['mf'] = null;
        }
        

        $data['created_by'] = Auth::user()->id;
        Client::create($data);
        return to_route('client.index')->with('success', 'Le client '. $request->name .' a été ajouter avec succée');
    }

    /**
     * Display the specified resource.
     */
    public function show(Client $client)
    {
        return view('client.show', compact('client'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Client $client)
    {
        return view('client.edit', ['client' => $client]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Client $client)
    {
        

        if ($request->type_client == 'Professionnel') {
           $data = $request->validate([
            'name' => 'required|string',
            'email' => ['required', 'email', Rule::unique('clients', 'email')->ignore($client->id)],
            'phone' => ['required', 'digits:8', Rule::unique('clients', 'phone')->ignore($client->id)],
            'mf' => ['required', 'string', Rule::unique('clients', 'mf')->ignore($client->id)],
            'address' => 'required|string',
            'sender' => 'required|string',
            'type_client' => 'required|in:Particulier,Professionnel',
            
        ], [
                'required' => 'Le champ est obligatoire',
                'digits' => 'Le numero de téléphone (8 chiffres) ',
                'mf.unique' => 'Matricule fiscal existe déja',
                'email.unique' => 'L\'email existe déja',
                'phone.unique' => 'Le Téléphone existe déja'
            ]);
        } else {
            $data = $request->validate([
            'name' => 'required|string',
            'email' => ['required', 'email', Rule::unique('clients', 'email')->ignore($client->id)],
            'phone' => ['required', 'digits:8', Rule::unique('clients', 'phone')->ignore($client->id)],
            'address' => 'required|string',
            'sender' => 'required|string',
            'type_client' => 'required|in:Particulier,Professionnel',
            
        ], [
                'required' => 'Le champ est obligatoire',
                'digits' => 'Vous dever sezire un numero de 8 chiffres',
                'email.unique' => 'L\'email existe déja',
                'phone.unique' => 'Le Téléphone existe déja'
            ]);
            $data['mf'] = null;
        }

        

        $data['created_by'] = Auth::user()->id;

        Client::findOrFail($client->id)->update($data);

        return to_route('client.index')->with('success', 'Le client '. $request->name .' a été modifier avec succée');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Client $client)
    {
        $client->delete();
        return to_route('client.index')->with('success', 'Le client '. $client->name .' a été supprimer avec succée');
    }
}
