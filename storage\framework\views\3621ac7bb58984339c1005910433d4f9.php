
<?php $__env->startSection('title'); ?>
    Client
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="content">

        <!-- Start Content-->
        <div class="container-xxl">

            <!-- Datatables  -->
            <div class="row py-3">
                <div class="col-md-12">
                    <div class="card">

                        <div class="card-header">
                                    <a href="<?php echo e(route('client.index')); ?>"
                                        class="btn btn-outline-info btn-sm"><i data-feather="arrow-left"></i></a>
                                        <div class="text-center">
                                            <h4><strong><?php echo e($client->name); ?></strong></h4>
                                        </div>
                                    
                        </div><!-- end card header -->

                        <div class="card-body">
                            <h5><strong>Nom:</strong> <?php echo e($client->name); ?></h5>
                            <h5><strong>Email:</strong> <?php echo e($client->email); ?></h5>
                            <h5><strong>Téléphone:</strong> <?php echo e($client->phone); ?></h5>
                            <h5><strong>Adresse:</strong> <?php echo e($client->address); ?></h5>
                            <h5><strong>Client depuit:</strong> <?php echo e($client->created_at->format('d-m-Y')); ?></h5>
                            <h5><strong>Envoyer Par:</strong> <?php echo e($client->sender); ?></h5>
                            <h5><strong>Créer Par: </strong>
                                <?php
                                    $user = App\Models\User::findOrFail($client->created_by);
                                ?>
                                <?php echo e($user->name); ?>

                            </h5>


                        </div>

                    </div>
                </div>
            </div>


            <!-- Button Datatable -->


        </div> <!-- container-fluid -->

    </div> <!-- content -->


    <!-- end Footer -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\client\show.blade.php ENDPATH**/ ?>