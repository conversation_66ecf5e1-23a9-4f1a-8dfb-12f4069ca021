<!DOCTYPE html>
<html lang="fr">
    <head>

        <meta charset="utf-8" />
        <title>Ajouter Utilisateur </title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc."/>
        <meta name="author" content="Zoyothemes"/>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />

        <!-- App favicon -->
        <link rel="shortcut icon" href="assets/images/favicon.ico">

        <!-- App css -->
        <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" id="app-style" />

        <!-- Icons -->
        <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />

    </head>

    <body class="bg-white">

        <!-- Begin page -->
        <div class="account-page">
            <div class="container-fluid p-0">        
                <div class="row align-items-center g-0">
                    <div class="col">
                        <div class="row">
                            <div class="col-md-3 mx-auto">
                                <div class="mb-0 border-0 p-md-5 p-lg-0 p-4">
                                    <div class="mb-4 p-0 text-center">
                                        <a href="<?php echo e(route('register')); ?>" class="auth-logo">
                                            <img src="<?php echo e(url('assets/images/logo-dark.png')); ?>" alt="logo-dark" class="mx-auto" height="58"/>
                                        </a>
                                    </div>
    
                                    <div class="pt-0">
                                        <form method="POST" action="<?php echo e(route('register')); ?>" enctype="multipart/form-data" class="my-4">
                                            <?php echo csrf_field(); ?>
                                            <div class="form-group mb-3">
                                                <label for="username" class="form-label">Nom<span class="text-danger"> *</span></label>
                                                <input class="form-control" name="name" type="text" id="name" required="" placeholder="Entrer le Nom">
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="emailaddress" class="form-label">Email<span class="text-danger"> *</span></label>
                                                <input class="form-control" type="email" name="email" id="emailaddress" required="" placeholder="Entrer l'email">
                                            </div>
                
                                            <div class="form-group mb-3">
                                                <label for="password" class="form-label">Mot De Passe<span class="text-danger"> *</span></label>
                                                <input class="form-control" type="password" name="password" required="" id="password" placeholder="Entrer le mot de passe">
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="username" class="form-label">Téléphone<span class="text-danger"> *</span></label>
                                                <input class="form-control" name="phone" type="text" id="phone" required="" placeholder="Entrer le téléphone">
                                            </div>

                                            <div class="form-group mb-3">
                                                <label for="username" class="form-label">Adresse<span class="text-danger"> *</span></label>
                                                <textarea class="form-control" name="address" type="text" id="address" required="" placeholder="Entrer l'adresse"></textarea>
                                            </div>

                                                                                       
                                            <div class="form-group mb-0 row">
                                                <div class="col-12">
                                                    <div class="d-grid">
                                                        <button class="btn btn-primary" type="submit"> Enregistrer</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>

                                        

                                       
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!-- END wrapper -->

        <!-- Vendor -->
        <script src="assets/libs/jquery/jquery.min.js"></script>
        <script src="assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
        <script src="assets/libs/simplebar/simplebar.min.js"></script>
        <script src="assets/libs/node-waves/waves.min.js"></script>
        <script src="assets/libs/waypoints/lib/jquery.waypoints.min.js"></script>
        <script src="assets/libs/jquery.counterup/jquery.counterup.min.js"></script>
        <script src="assets/libs/feather-icons/feather.min.js"></script>

        <!-- App js-->
        <script src="assets/js/app.js"></script>
        
    </body>
</html><?php /**PATH C:\xampp\htdocs\new\resources\views\auth\register.blade.php ENDPATH**/ ?>