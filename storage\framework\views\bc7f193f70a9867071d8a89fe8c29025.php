<!DOCTYPE html>
<html lang="fr">

<head>

    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title'); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc." />
    <meta name="author" content="Zoyothemes" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="<?php echo e(url('assets/images/favicon.ico')); ?>">

    <link href="<?php echo e(url('assets/libs/datatables.net-bs5/css/dataTables.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />
    <link href="<?php echo e(url('assets/libs/datatables.net-buttons-bs5/css/buttons.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />
    <link href="<?php echo e(url('assets/libs/datatables.net-keytable-bs5/css/keyTable.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />
    <link href="<?php echo e(url('assets/libs/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css')); ?>"
        rel="stylesheet" type="text/css" />
    <link href="<?php echo e(url('assets/libs/datatables.net-select-bs5/css/select.bootstrap5.min.css')); ?>" rel="stylesheet"
        type="text/css" />

    <!-- App css -->
    <link href="<?php echo e(url('assets/css/app.min.css')); ?>" rel="stylesheet" type="text/css" id="app-style" />

    <!-- Icons -->
    <link href="<?php echo e(url('assets/css/icons.min.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Dark Mode CSS -->
    <link href="<?php echo e(url('assets/css/dark-mode.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Dark Mode Styles -->
    <link href="<?php echo e(url('assets/css/dark-mode.css')); ?>" rel="stylesheet" type="text/css" />

</head>

<!-- body start -->

<body data-menu-color="light" data-sidebar="default">

    <!-- Begin page -->
    <div id="app-layout">

        <!-- Topbar Start -->
        <?php echo $__env->make('inc.nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!-- end Topbar -->

        <!-- Left Sidebar Start -->
        <?php echo $__env->make('inc.aside', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start Page Content here -->
        <!-- ============================================================== -->

        <div class="content-page">
            <?php echo $__env->make('inc.alerts.success', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php echo $__env->make('inc.alerts.error', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <?php echo $__env->yieldContent('content'); ?>


            <!-- content -->

            <!-- Footer Start -->
            <?php echo $__env->make('inc.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <!-- end Footer -->

        </div>
        <!-- ============================================================== -->
        <!-- End Page content -->
        <!-- ============================================================== -->

    </div>
    <!-- END wrapper -->

    <script src="<?php echo e(url('assets/libs/jquery/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/simplebar/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/node-waves/waves.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/waypoints/lib/jquery.waypoints.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/jquery.counterup/jquery.counterup.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/feather-icons/feather.min.js')); ?>"></script>

    <!-- Apexcharts JS -->
    <script src="<?php echo e(url('assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>

    <!-- for basic area chart -->
    <script src="<?php echo e(url('assets/js/stock-prices.js')); ?>"></script>

    <!-- Widgets Init Js -->
    <script src="<?php echo e(url('assets/js/pages/ecommerce-dashboard.init.js')); ?>"></script>

    <script src="<?php echo e(url('assets/libs/fullcalendar/index.global.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net/js/jquery.dataTables.min.js')); ?>"></script>

    <!-- dataTables.bootstrap5 -->
    <script src="<?php echo e(url('assets/libs/datatables.net-bs5/js/dataTables.bootstrap5.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons/js/dataTables.buttons.min.js')); ?>"></script>

    <!-- buttons.colVis -->
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons/js/buttons.colVis.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons/js/buttons.flash.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons/js/buttons.html5.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons/js/buttons.print.min.js')); ?>"></script>

    <!-- buttons.bootstrap5 -->
    <script src="<?php echo e(url('assets/libs/datatables.net-buttons-bs5/js/buttons.bootstrap5.min.js')); ?>"></script>

    <!-- dataTables.keyTable -->
    <script src="<?php echo e(url('assets/libs/datatables.net-keytable/js/dataTables.keyTable.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-keytable-bs5/js/keyTable.bootstrap5.min.js')); ?>"></script>

    <!-- dataTable.responsive -->
    <script src="<?php echo e(url('assets/libs/datatables.net-responsive/js/dataTables.responsive.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js')); ?>"></script>

    <!-- dataTables.select -->
    <script src="<?php echo e(url('assets/libs/datatables.net-select/js/dataTables.select.min.js')); ?>"></script>
    <script src="<?php echo e(url('assets/libs/datatables.net-select-bs5/js/select.bootstrap5.min.js')); ?>"></script>

    <!-- Datatable Demo App Js -->
    <script src="<?php echo e(url('assets/js/pages/datatable.init.js')); ?>"></script>


    <!-- App js-->
    <script src="<?php echo e(url('assets/js/app.js')); ?>"></script>

    <script>
        $(function() {

            $("#table1").DataTable({

                "language": {

                    "sProcessing": "Traitement en cours...",

                    "sSearch": "Chèrcher:",

                    "sLengthMenu": "Afficher _MENU_ &eacute;l&eacute;ments",

                    "sInfo": "Affichage de l'&eacute;l&eacute;ment _START_ &agrave; _END_ sur _TOTAL_ &eacute;l&eacute;ments",

                    "sInfoEmpty": "Affichage de l'&eacute;l&eacute;ment 0 &agrave; 0 sur 0 &eacute;l&eacute;ment",

                    "sInfoFiltered": "(filtr&eacute; de _MAX_ &eacute;l&eacute;ments au total)",

                    "sInfoPostFix": "",

                    "sLoadingRecords": "Chargement en cours...",

                    "sZeroRecords": "Aucun &eacute;l&eacute;ment &agrave; afficher",

                    "sEmptyTable": "Aucune donn&eacute;e disponible dans le tableau",

                    "oPaginate": {

                        "sFirst": "Premier",

                        "sPrevious": "<",

                        "sNext": ">",

                        "sLast": "Dernier"

                    },

                    "oAria": {

                        "sSortAscending": ": activer pour trier la colonne par ordre croissant",

                        "sSortDescending": ": activer pour trier la colonne par ordre d&eacute;croissant"

                    },

                },

                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "ordering": true,
                "paging": true,

            }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');



        });
    </script>

    <script>
        // Dark Mode Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const lightIcon = document.querySelector('.light-icon');
            const darkIcon = document.querySelector('.dark-icon');
            const body = document.body;
            
            // Check system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            // Get saved theme or use system preference
            const savedTheme = localStorage.getItem('theme') || (prefersDark ? 'dark' : 'light');
            
            // Apply initial theme
            if (savedTheme === 'dark') {
                body.setAttribute('data-theme', 'dark');
                lightIcon.classList.add('d-none');
                darkIcon.classList.remove('d-none');
            } else {
                body.removeAttribute('data-theme');
                lightIcon.classList.remove('d-none');
                darkIcon.classList.add('d-none');
            }
            
            // Toggle theme function
            themeToggle.addEventListener('click', function() {
                const currentTheme = body.getAttribute('data-theme');
                
                if (currentTheme === 'dark') {
                    body.removeAttribute('data-theme');
                    lightIcon.classList.remove('d-none');
                    darkIcon.classList.add('d-none');
                    localStorage.setItem('theme', 'light');
                } else {
                    body.setAttribute('data-theme', 'dark');
                    lightIcon.classList.add('d-none');
                    darkIcon.classList.remove('d-none');
                    localStorage.setItem('theme', 'dark');
                }
            });
        });
    </script>

</body>

</html>
<?php /**PATH C:\xampp\htdocs\new\resources\views/layout.blade.php ENDPATH**/ ?>