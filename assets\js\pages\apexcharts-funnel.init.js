"use strict";var options={series:[{name:"Funnel Series",data:[1380,1100,990,880,740,548,330,200]}],chart:{type:"bar",height:350,parentHeightOffset:0},colors:["#537AEF"],plotOptions:{bar:{borderRadius:0,horizontal:!0,barHeight:"80%",isFunnel:!0}},dataLabels:{enabled:!0,formatter:function(e,t){return t.w.globals.labels[t.dataPointIndex]+":  "+e},dropShadow:{enabled:!0}},title:{text:"Recruitment Funnel",align:"middle"},xaxis:{categories:["Sourced","Screened","Assessed","HR Interview","Technical","Verify","Offered","Hired"]},legend:{show:!1}},chart=new ApexCharts(document.querySelector("#funnel_chart"),options);chart.render();options={series:[{name:"",data:[200,330,548,740,880,990,1100,1380]}],chart:{type:"bar",height:350,parentHeightOffset:0},plotOptions:{bar:{borderRadius:0,horizontal:!0,distributed:!0,barHeight:"80%",isFunnel:!0}},colors:["#29aa85","#eb9d59","#e68434","#ec8290","#963b68","#8c57d1","#522c8f","#537AEF"],dataLabels:{enabled:!0,formatter:function(e,t){return t.w.globals.labels[t.dataPointIndex]},dropShadow:{enabled:!0}},title:{text:"Pyramid Chart",align:"middle"},xaxis:{categories:["Sweets","Processed Foods","Healthy Fats","Meat","Beans & Legumes","Dairy","Fruits & Vegetables","Grains"]},legend:{show:!1}};(chart=new ApexCharts(document.querySelector("#pyramid_funnel_chart"),options)).render();