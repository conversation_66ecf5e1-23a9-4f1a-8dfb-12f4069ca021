"use strict";
document.addEventListener("DOMContentLoaded", function () {
  var e = document.getElementById("calendar");
  const today = new Date();
  const day = today.getDate().toString().padStart(2, '0');
  const month = (today.getMonth() + 1).toString().padStart(2, '0');
  const year = today.getFullYear()
  const formattedDate = `${year}-${month}-${day}`;
  var calendar = new FullCalendar.Calendar(e, {
    locale: "fr", // <--- اللغة الفرنسية// اختياري، للمنطقة الزمنية فقط
    editable: !0,
    selectable: !0,
    initialView:
      768 <= window.innerWidth && window.innerWidth < 1200
        ? "timeGridWeek"
        : window.innerWidth <= 768
        ? "listMonth"
        : "dayGridMonth",
    themeSystem: "bootstrap",
    headerToolbar: {
      left: "prev,next today",
      center: "title",
      right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
    },
    initialDate: formattedDate,
    weekNumbers: !1,
    dayMaxEvents: !0,
    handleWindowResize: !0,
    events: [
      { title: "All Day Event", start: "2025-07-01T08:00:00", className: "event-danger border-danger" },
      { title: "All Day Event", start: "2025-07-01T08:30:00", className: "event-primary border-primary" },
      { title: "All Day Event", start: "2025-07-01T09:00:00", className: "event-warning border-warning" },
      { title: "All Day Event", start: "2025-07-01T09:30:00", className: "event-secondary border-secondary" },
      { title: "All Day Event", start: "2025-07-01T10:00:00", className: "event-info border-info" },
      { title: "All Day Event", start: "2025-07-01T10:30:00", className: "event-dark border-dark" },
      { title: "All Day Event",url : window.location.origin + '/new' , start: "2025-07-01T11:00:00", className: "" },
      { title: "Long Event", start: "2025-01-07", end: "2025-01-10", className: "event-secondary border-secondary" },
      { title: "Repeating Event", start: "2025-01-09T16:00:00", className: "event-danger border-danger" },
      { title: "Repeating Event", start: "2025-01-16T16:00:00", className: "event-primary border-primary" },
      { title: "Conference", start: "2025-01-24", end: "2025-01-27", className: "event-primary border-primary" },
      { title: "Meeting", start: "2025-01-12T10:30:00", end: "2025-01-12T12:30:00", className: "event-primary border-primary" },
      { title: "Lunch", start: "2025-01-12T12:00:00", className: "event-secondary border-secondary" },
      { title: "Meeting", start: "2025-01-12T14:30:00", className: "event-danger border-danger" },
      { title: "Happy Hour", start: "2025-01-12T17:30:00", className: "event-warning border-warning" },
      { title: "Dinner", start: "2025-01-12T20:00:00", className: "event-info border-info" },
      { title: "Birthday Party", start: "2025-01-13T07:00:00", className: "event-dark border-dark" },
      { title: "Click for Google", url: "http://google.com/", start: "2025-01-28", className: "event-primary border-primary" }
    ]
  });
  calendar.render();
  
});
