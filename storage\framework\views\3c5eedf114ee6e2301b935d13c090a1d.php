 
 <?php $__env->startSection('title'); ?>
     Calendrier
 <?php $__env->stopSection(); ?>
 <?php $__env->startSection('content'); ?>
     <style>
         .event-primary {
             background-color: #537AEF !important;
             color: white !important;
         }

         .event-primary:hover {
             background-color: #4b6ed7 !important;
             color: black !important;
         }
          .event-success {
             background-color: #29aa85 !important;
             color: white !important;
         }

         .event-success:hover {
             background-color: #259978 !important;
             color: black !important;
         }
         .event-danger {
             background: #ec8290 !important;
             color: white !important;
         }

         .event-danger:hover {
             background:  #d47582 !important;
             color: black !important;
         }
         .event-warning {
             background-color: #eb9d59 !important;
             color: white !important;
         }

         .event-warning:hover {
             background: #d48d50 !important;
             color: black !important;
         }
         .fc-popover-body{
            overflow-y: scroll;
            max-height: 160px;
         }
     </style>

     <div class="content">

         <!-- Start Content-->
         <div class="container-xxl">

             <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
             </div>

             <div class="row ">
                 <div class="col-12">
                     <div class="row">
                         <div class="col-xl-12 col-lg-12">
                             <div class="card">

                                 <div class="card-header">
                                     <a href="<?php echo e(route('rdv.create')); ?>" class="btn btn-success float-end">Réception</a>
                                 </div>
                                 <div class="card-body app-calendar">
                                     <div id="calendar"></div>
                                 </div> <!-- end card-body -->

                             </div> <!-- end card-->
                         </div> <!-- end col -->
                     </div> <!-- end row -->
                 </div> <!-- end col -->
             </div> <!-- end row -->
         </div> <!-- container-fluid -->

     </div>

     <script>
         "use strict";
         const rdvs = <?php echo json_encode($rdvs, 15, 512) ?>;
         const showRoute = "<?php echo e(route('rdv.show', ':id')); ?>";
         const events = rdvs.map(rdv => ({
             title: rdv.car.marque+' '+ rdv.car.model,
             start: rdv.date + ' ' + rdv.time,
             end: rdv.updated_at - rdv.date,
             className: rdv.status === 'Immediat' ? 
             'border-dark event-success' : rdv.status === 'Fixé' ? 
             'border-dark event-primary' : rdv.status === 'En attente'?
             'border-dark event-warning' : 'border-dark event-danger',
             url : `${showRoute.replace(':id', rdv.id)}`,
         }));
         document.addEventListener("DOMContentLoaded", function() {
             var e = document.getElementById("calendar");
             
             
            
             const today = new Date();
             const day = today.getDate().toString().padStart(2, '0');
             const month = (today.getMonth() + 1).toString().padStart(2, '0');
             const year = today.getFullYear()
             const formattedDate = `${year}-${month}-${day}`;
             var calendar = new FullCalendar.Calendar(e, {
                 locale: "fr",
                 editable: !1,
                 selectable: !0,
                 initialView: 768 <= window.innerWidth && window.innerWidth < 1200 ?
                     "listWeek" : window.innerWidth <= 768 ?
                     "dayGridMonth" : "dayGridMonth",
                 themeSystem: "bootstrap",
                 headerToolbar: {
                     left: "prev,next today",
                     center: "title",
                     right: "dayGridMonth,listWeek"
                 },
                 noEventsText:"Aucun rendez-vous à afficher",
                 moreLinkText: n => `+${n} encore`,
                 weekText: 'S:',
                 initialDate: formattedDate,
                 weekNumbers: !1,
                 dayMaxEvents: !0,
                 handleWindowResize: !1,
                 events: events
             });
             calendar.render();
             
             
                // console.log(more);
            
         });
         document.getElementsByClassName('fc-more-link').textContent = 'voir +';
     </script>
 <?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views/rdv/index.blade.php ENDPATH**/ ?>