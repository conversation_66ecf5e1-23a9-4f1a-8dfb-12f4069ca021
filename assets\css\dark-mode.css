/* Dark Mode Styles */
[data-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #e9ecef;
    --bs-dark: #ffffff;
    --bs-light: #2d3748;
}

/* Theme toggle button styles */
#theme-toggle {
    font-size: 18px;
    padding: 8px 12px;
    border: none;
    background: transparent;
    cursor: pointer;
}

#theme-toggle:hover {
    background-color: rgba(0,0,0,0.1);
    border-radius: 4px;
}

[data-theme="dark"] body {
    background-color: #1a1a1a !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .topbar-custom {
    background-color: #2d3748 !important;
    border-bottom-color: #4a5568 !important;
}

[data-theme="dark"] .app-sidebar-menu {
    background-color: #2d3748 !important;
    border-right-color: #4a5568 !important;
}

[data-theme="dark"] .card {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .card-header {
    background-color: #4a5568 !important;
    border-bottom-color: #718096 !important;
}

[data-theme="dark"] .table {
    background-color: #2d3748 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .table th {
    background-color: #4a5568 !important;
    color: #e9ecef !important;
    border-color: #718096 !important;
}

[data-theme="dark"] .table td {
    border-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .dropdown-menu {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
}

[data-theme="dark"] .dropdown-item {
    color: #e9ecef !important;
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: #4a5568 !important;
}

[data-theme="dark"] .text-muted {
    color: #a0aec0 !important;
}

[data-theme="dark"] .text-dark {
    color: #e9ecef !important;
}

[data-theme="dark"] .bg-light {
    background-color: #4a5568 !important;
}

[data-theme="dark"] .content-page {
    background-color: #1a1a1a !important;
}

[data-theme="dark"] .menu-title {
    color: #a0aec0 !important;
}

[data-theme="dark"] .tp-link {
    color: #e9ecef !important;
}

[data-theme="dark"] .tp-link:hover {
    color: #ffffff !important;
    background-color: #4a5568 !important;
}

[data-theme="dark"] .noti-title {
    background-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .profile-dropdown {
    background-color: #2d3748 !important;
}

[data-theme="dark"] .border-dark {
    border-color: #4a5568 !important;
}

[data-theme="dark"] .widget-icons-sections {
    background-color: #4a5568 !important;
}

[data-theme="dark"] .fs-14,
[data-theme="dark"] .fs-20 {
    color: #e9ecef !important;
}

[data-theme="dark"] .text-success {
    color: #68d391 !important;
}

[data-theme="dark"] .text-danger {
    color: #fc8181 !important;
}

[data-theme="dark"] .bg-primary-subtle {
    background-color: #4a5568 !important;
}

[data-theme="dark"] .list-group-item {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .avatar {
    border-color: #4a5568 !important;
}

[data-theme="dark"] .btn-primary {
    background-color: #3182ce !important;
    border-color: #3182ce !important;
}

[data-theme="dark"] .btn-secondary {
    background-color: #4a5568 !important;
    border-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .badge {
    color: #1a1a1a !important;
}

[data-theme="dark"] .form-control {
    background-color: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e9ecef !important;
}

[data-theme="dark"] .form-control:focus {
    background-color: #2d3748 !important;
    border-color: #3182ce !important;
    color: #e9ecef !important;
}
