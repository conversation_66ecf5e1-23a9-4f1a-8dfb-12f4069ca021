<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rdv extends Model
{
    protected $fillable = [
        'date', 'time', 'status', 'etape', 'created_by', 'car_id',
    ];
    public function car()
    {
        return $this->belongsTo(Car::class);
    }
    public function photoAv()
    {
        return $this->hasMany(PhotoAv::class);
    }
    public function photoAp()
    {
        return $this->hasMany(PhotoAp::class);
    }
    public function files()
    {
        return $this->hasMany(File::class);
    }
}
