
<?php $__env->startSection('title', 'Ajout Véhicule'); ?>


<?php $__env->startSection('content'); ?>
    <style>
        #resultV,
        #resultC {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            list-style: none;
            padding: 0;
            margin-top: 1px;
            max-height: 200px;
            width: 100%;
            overflow: auto;
            z-index: 1000;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        #resultV li,
        #resultC li {
            padding: 8px 12px;
            cursor: pointer;
        }

        #resultV li:hover,
        #resultC li:hover {
            background-color: #f0f0f0;
        }
    </style>
    <div class="content">

        <div class="row py-3">
            <div class="col-md-3"></div>

            <!-- Grids Modal -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col">
                                <h4><strong>Ajouter Véhicule</strong></h4>
                            </div>
                            <div class="col">
                                <a href="<?php echo e(route('car.index')); ?>"
                                    class="btn btn-outline-warning btn-sm float-end">Retour</a>
                            </div>
                        </div>
                    </div><!-- end card header -->

                    <div class="card-body">
                        <form action="<?php echo e(route('car.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <label for="rechercheV" class="form-label">Recerche Véhicule</label>
                                    <input type="text" name="rechercheV"
                                        class="form-control <?php $__errorArgs = ['rechercheV'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="rechercheV"
                                        placeholder="Cherchez par immat.. ou par N°: chassis">
                                </div>
                                <div class="col-md-4">
                                    <label for="rechercheC" class="form-label">Recerche Client</label>
                                    <input type="text" name="rechercheC"
                                        class="form-control <?php $__errorArgs = ['rechercheC'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="rechercheC"
                                        placeholder="Cherchez par N°: Tel Nom ou par email">
                                </div>
                                <div class="col-md-4">
                                    <label for="client_id" class="form-label">Client</label>
                                    <select name="client_id"
                                        class="form-control client <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="client_id">
                                        <option value="">Choisissez...</option>
                                        <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e(old('client_id', $client->id)); ?>">
                                                <?php echo e(old('client_id', $client->name)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </select>
                                    <?php $__errorArgs = ['client_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <hr>
                            <div class="row g-2">                               
                                
                                <div class="col-md-4">
                                    <label for="car" class="form-label">Véhicules</label>
                                    <select name="car" class="form-control car <?php $__errorArgs = ['car'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="car">
                                        <option value="">Choisissez...</option>
                                    </select>
                                    <?php $__errorArgs = ['car'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-4">
                                    <label for="marque" class="form-label">Marque</label>
                                    <select name="marque" class="form-control marque <?php $__errorArgs = ['marque'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="marque">
                                        <option value="">Choisissez...</option>
                                        <?php $__currentLoopData = $marques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $marque): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e(old('marque', $marque->id)); ?>">
                                                <?php echo e(old('marque', $marque->marque)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </select>
                                    <?php $__errorArgs = ['marque'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-4">
                                    <label for="modele" class="form-label">Modele</label>
                                    <select name="modele" class="form-control <?php $__errorArgs = ['modele'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        id="modele">
                                        <option value="">Choisissez...</option>
                                    </select>
                                    <?php $__errorArgs = ['modele'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="col-md-4">
                                    <label for="name" class="form-label">Nom</label>
                                    <input type="text" name="name"
                                        class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name"
                                        placeholder="Entrer le nom" value="<?php echo e(old('name')); ?>">
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-4">
                                        <label for="prename" class="form-label">Prénom</label>
                                        <input type="text" name="prename"
                                            class="form-control <?php $__errorArgs = ['prename'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="prename"
                                            placeholder="Entrer le prénom" value="<?php echo e(old('prename')); ?>">
                                        <?php $__errorArgs = ['prename'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-4">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" name="email"
                                        class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email"
                                        placeholder="Entrer l'email" value="<?php echo e(old('email')); ?>">
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div><!--end col-->

                                <div class="col-md-4">
                                    <label for="phone" class="form-label">Téléphone</label>
                                    <input type="text" name="phone"
                                        class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone"
                                        placeholder="Entrer le téléphone" value="<?php echo e(old('phone')); ?>">
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div><!--end col-->

                                
                                <div class="col-md-4">
                                        <label for="sender" class="form-label">Envoyeur</label>
                                        <input type="text" name="sender"
                                            class="form-control <?php $__errorArgs = ['sender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sender"
                                            placeholder="Entrer l'envoyeur" value="<?php echo e(old('sender')); ?>">
                                        <?php $__errorArgs = ['sender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                                <div class="col-md-4">
                                        <label for="address" class="form-label">Adresse</label>
                                        <textarea type="text" name="address" class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="address"
                                            placeholder="Entrer l'adresse"> <?php echo e(old('address')); ?></textarea>
                                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div><!--end col-->

                                <div class="col-md-12">
                                    <div class="hstack gap-2 justify-content-center">
                                        <button type="submit" class="btn btn-primary">Ajoutez</button>
                                    </div>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </form> <!-- end form -->
                    </div><!-- end card-body -->
                </div><!-- end card -->
            </div> <!--end col-->
        </div> <!-- end row -->
        <ul style="display: none;" id="resultV"></ul>
        <ul style="display: none;" id="resultC"></ul>
    </div>
    <script>
        const modeles = <?php echo json_encode($modeles, 15, 512) ?>;
        const cars = <?php echo json_encode($cars, 15, 512) ?>

        const clients = <?php echo json_encode($clients, 15, 512) ?>;
        const client_id = document.getElementById('client_id');
        const searchV = document.getElementById('rechercheV');
        const searchC = document.getElementById('rechercheC');
        const resultV = document.getElementById('resultV');

        const marque = document.querySelector('.marque');
        const selectModel = document.getElementById('modele');

        const selectCar = document.getElementById('car');

        selectCar.addEventListener('change', function() {
            marque.options[marque.selectedIndex].textContent = this.options[this.selectedIndex].dataset
                .marque;
            selectModel.innerHTML = '<option value="">Choisissez...</option>'

            const option = document.createElement('option');
            option.value = this.options[this.selectedIndex].dataset.modele;
            option.textContent = this.options[this.selectedIndex].dataset.modele;
            selectModel.appendChild(option);


        });

        marque.addEventListener('change', function() {
            const element = modeles.filter(modele => modele.id_marque == parseInt(this.options[this
                .selectedIndex].value));
            selectModel.innerHTML = '<option value="">Choisissez...</option>'
            element.forEach(model => {
                const option = document.createElement('option');
                option.value = model.id;
                option.textContent = model.modele;
                selectModel.appendChild(option);
            });

        });

        

        searchV.addEventListener('input', function() {
            const key = this.value.toLowerCase();
            resultV.innerHTML = '';

            if (key === "") {
                resultV.style.display = "none";
                return;
            }
            const element = cars.filter(car => car.immatriculation.toLowerCase().includes(key) || car.n_chassis
                .toLowerCase().includes(key));

            if (element.length === 0) {
                resultV.style.display = "none";
                return;
            }

            const rect = searchV.getBoundingClientRect();
            resultV.style.top = `${rect.bottom + window.scrollY}px`;
            resultV.style.left = `${rect.left+ window.scrollX}px`;
            resultV.style.width = `${rect.width}px`;
            resultV.style.display = "block";

            element.forEach(model => {
                const li = document.createElement('li');
                li.textContent = `${model.marque} - ${model.model} - ${model.immatriculation}`;
                li.style.cursor = 'pointer';
                li.style.padding = '5px';
                resultV.appendChild(li);
            });
            resultV.style.display = "block";

        });

        searchC.addEventListener('input', function() {
            const key = this.value.toLowerCase();
            resultC.innerHTML = '';

            if (key === "") {
                resultC.style.display = "none";
                return;
            }
            const element = clients.filter(client => client.email.toLowerCase().includes(key) || client.phone
                .toLowerCase().includes(key) || client.name.toLowerCase().includes(key));

            if (element.length === 0) {
                resultC.style.display = "none";
                return;
            }

            const rect = searchC.getBoundingClientRect();
            resultC.style.top = `${rect.bottom + window.scrollY}px`;
            resultC.style.left = `${rect.left+ window.scrollX}px`;
            resultC.style.width = `${rect.width}px`;
            resultC.style.display = "block";

            element.forEach(model => {
                const li = document.createElement('li');
                li.textContent = `${model.name} - ${model.phone} - ${model.email}`;
                li.style.cursor = 'pointer';
                li.style.padding = '5px';
                resultC.appendChild(li);
            });
            resultC.style.display = "block";

        });
        document.addEventListener("click", function(event){
            const isClickInsideInputV = searchV.contains(event.target);
            const isClickInsideInputC = searchC.contains(event.target);
            const isClickInsideListV = resultV.contains(event.target);
            const isClickInsideListC = resultC.contains(event.target);
            if(!isClickInsideInputV && !isClickInsideListV){
                resultV.style.display = "none";
            }
            if(!isClickInsideInputC && !isClickInsideListC){
                resultC.style.display = "none";
            }

        })
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\car\create.blade.php ENDPATH**/ ?>