
<?php $__env->startSection('title', 'Réception'); ?>


<?php $__env->startSection('content'); ?>
    <style>
        #resultV,
        #resultC {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            list-style: none;
            padding: 0;
            margin-top: 1px;
            max-height: 200px;
            width: 100%;
            overflow: auto;
            z-index: 1000;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        #resultV li,
        #resultC li {
            padding: 8px 12px;
            cursor: pointer;
        }

        #resultV li:hover,
        #resultC li:hover {
            background-color: #f0f0f0;
        }
    </style>
    <div class="content">

        <div class="row py-3">
            <div class="col-md-3"></div>

            <!-- Grids Modal -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col">
                                <h4><strong>Réception</strong></h4>
                            </div>
                            <div class="col">
                                <a href="<?php echo e(route('rdv.index')); ?>"
                                    class="btn btn-outline-warning btn-sm float-end">Retour</a>
                            </div>
                        </div>
                    </div><!-- end card header -->

                    <div class="card-body">
                        <form action="<?php echo e(route('rdv.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="row g-2">
                                <h4 class="text-center">Recherche</h4>
                                <div class="col-md-3">
                                    <label for="rechercheV" class="form-label">Par Véhicule</label>
                                    <input type="text" name="rechercheV"
                                        class="form-control <?php $__errorArgs = ['rechercheV'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="rechercheV"
                                        placeholder="Cherchez par marque modele immat.. N° chas..">
                                </div>
                                <div class="col-md-3">
                                    <label for="rechercheC" class="form-label">Par Client</label>
                                    <input type="text" name="rechercheC"
                                        class="form-control <?php $__errorArgs = ['rechercheC'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="rechercheC"
                                        placeholder="Cherchez par N°: Tel Nom ou par email">
                                    <input type="hidden" name="client_id" id="client_id">
                                </div>
                                <div class="col-md-4">
                                    <label for="listV" class="form-label">Liste des véhicules</label>
                                    <select name="listV" class="form-control" id="listV">
                                    </select>
                                </div>
                                <div class="col-md-1">
                                    <label id="iconText" for="btnNew" class="form-label">Nouveau</label>
                                    <span id="btnNew" class="form-control btn btn-success btn-sm"><i id="icon"
                                            class="mdi mdi-plus fs-18"></i></span>
                                </div>

                            </div>
                            <hr>
                            <div class="row g-2">

                                <div id="carInfo" style="display: none;" class="col-md-12">
                                    <h4 class="text-center">Informtion véhicule</h4>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="marque" class="form-label">Marque</label>
                                            <select name="marque"
                                                class="form-control marque <?php $__errorArgs = ['marque'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="marque">
                                                <option value="">Choisissez...</option>
                                                <?php $__currentLoopData = $marques; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $marque): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e(old('marque', $marque->id)); ?>">
                                                        <?php echo e(old('marque', $marque->marque)); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                            </select>
                                            <?php $__errorArgs = ['marque'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="modele" class="form-label">Modele</label>
                                            <select name="modele"
                                                class="form-control <?php $__errorArgs = ['modele'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="modele">
                                                <option value="">Choisissez...</option>
                                            </select>
                                            <?php $__errorArgs = ['modele'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>

                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="immatriculation" class="form-label">Immatriculation</label>
                                            <input type="text" name="immatriculation"
                                                class="form-control <?php $__errorArgs = ['immatriculation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="immatriculation" placeholder="Entrer l'immatriculation"
                                                value="<?php echo e(old('immatriculation')); ?>">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="n_chassis" class="form-label">N°: Chassis</label>
                                            <input minlength="17" maxlength="17" type="text" name="n_chassis"
                                                class="form-control <?php $__errorArgs = ['n_chassis'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="n_chassis"
                                                placeholder="Entrer le N°: Chassis" value="<?php echo e(old('n_chassis')); ?>">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="dpmc" class="form-label">Date mise en circulation</label>
                                            <input max="<?php echo e(date('Y-m-d')); ?>" type="date" name="dpmc"
                                                class="form-control <?php $__errorArgs = ['dpmc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="dpmc"
                                                placeholder="Entrer la date mise en circulation"
                                                value="<?php echo e(old('dpmc')); ?>">
                                        </div>
                                    </div>

                                </div>
                                <hr>
                                <div id="clientInfo" style="display: none;" class="col-md-12">
                                    <h4 class="text-center">Informtion client</h4>
                                    <div class="row">
                                        <div class="col-md-2">
                                            <label for="name" class="form-label">Nom</label>
                                            <input type="text" name="name"
                                                class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name"
                                                placeholder="Entrer l'immatriculation" value="<?php echo e(old('name')); ?>">
                                        </div>
                                        <div class="col-md-2">
                                            <label for="phone" class="form-label">Téléphone</label>
                                            <input minlength="8" maxlength="8" type="text" name="phone"
                                                class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="phone"
                                                placeholder="Entrer le N°: Chassis" value="<?php echo e(old('phone')); ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" name="email"
                                                class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="email"
                                                placeholder="Entrer la date mise en circulation"
                                                value="<?php echo e(old('email')); ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="address" class="form-label">Adresse</label>
                                            <textarea type="text" name="address" class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="address"
                                                placeholder="Entrer la date mise en circulation"><?php echo e(old('address')); ?></textarea>
                                        </div>
                                        <div class="col-md-2">
                                            <label for="sender" class="form-label">Envoyeur</label>
                                            <input type="text" name="sender"
                                                class="form-control <?php $__errorArgs = ['sender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="sender"
                                                placeholder="Entrer le N°: Chassis" value="<?php echo e(old('sender')); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="hstack gap-2 justify-content-center">
                                        <button type="submit" class="btn btn-primary">Enregistrez</button>
                                    </div>
                                </div><!-- end col -->
                            </div><!-- end row -->
                        </form> <!-- end form -->
                    </div><!-- end card-body -->
                </div><!-- end card -->
            </div> <!--end col-->
        </div> <!-- end row -->
        <ul style="display: none;" id="resultV"></ul>
        <ul style="display: none;" id="resultC"></ul>
    </div>
    <script>
        const modeles = <?php echo json_encode($modeles, 15, 512) ?>,
            cars = <?php echo json_encode($cars, 15, 512) ?>,
            clients = <?php echo json_encode($clients, 15, 512) ?>;

        const el = id => document.getElementById(id),
            marque = document.querySelector('.marque'),
            selectModel = el('modele'),
            resultV = el('resultV'),
            resultC = el('resultC'),
            listV = el('listV'),
            carInfo = el('carInfo'),
            clientInfo = el('clientInfo'),
            icon = el('icon'),
            inputs = ['immatriculation', 'n_chassis', 'dpmc', 'name', 'phone', 'email', 'address', 'sender'].map(el),
            btnNew = el('btnNew'),
            searchV = el('rechercheV'),
            searchC = el('rechercheC'),
            client_id = el('client_id');

        const clearFields = () => {
            inputs.forEach(input => input.value = '');
            if (marque.options.length > 0) {
                marque.options[0].textContent = 'Choisissez...';
                marque.options[0].value = '';
            }
            selectModel.innerHTML = '<option value="">Choisissez...</option>';
            listV.innerHTML = '<option value="">Choisissez...</option>';
        };

        const toggleSection = (show = true) => {
                carInfo.style.display = clientInfo.style.display = show ? 'block' : 'none';
            },
            fillCarForm = car => {
                marque.options[marque.selectedIndex].textContent = car.marque;
                marque.options[marque.selectedIndex].marque = car.marque;
                selectModel.innerHTML = `<option>${car.model}</option>`;
                inputs[0].value = car.immatriculation;
                inputs[1].value = car.n_chassis.toUpperCase();
                inputs[2].value = car.dpmc;
                fillClientForm(car.client);
            },
            fillClientForm = client => {
                inputs[3].value = client.name;
                inputs[4].value = client.phone;
                inputs[5].value = client.email;
                inputs[6].value = client.address;
                inputs[7].value = client.sender;
                searchC.value = client.name;
                client_id.value = client.id;
            };

        marque.addEventListener('change', e => {
            const id = e.target.value;
            selectModel.innerHTML = `<option value="">Choisissez...</option>` + modeles
                .filter(m => m.id_marque == id)
                .map(m => `<option value="${m.id}">${m.modele}</option>`).join('');
        });

        const searchHandler = (input, resultBox, dataList, formatter, onSelect) => {
            input.addEventListener('input', () => {
                const key = input.value.trim().toLowerCase();
                resultBox.innerHTML = '';
                if (!key) return resultBox.style.display = 'none';
                const filtered = dataList.filter(formatter(key));
                if (!filtered.length) return resultBox.style.display = 'none';
                resultBox.style.display = 'block';
                resultBox.style.top = `${input.getBoundingClientRect().bottom + window.scrollY}px`;
                resultBox.style.left = `${input.getBoundingClientRect().left + window.scrollX}px`;
                resultBox.style.width = `${input.getBoundingClientRect().width}px`;
                filtered.forEach(item => {
                    const li = document.createElement('li');
                    li.textContent = onSelect.label(item);
                    li.onclick = () => onSelect.action(item);
                    resultBox.appendChild(li);
                });
            });
        };

        searchHandler(searchV, resultV, cars,
            key => car => [car.marque, car.model, car.immatriculation, car.n_chassis].some(val => val.toLowerCase()
                .includes(key)), {
                label: c => `${c.marque} - ${c.model} - ${c.immatriculation}`,
                action: car => {
                    listV.innerHTML =
                        `<option value="${car.id}">${car.marque} - ${car.model} - ${car.immatriculation}</option>`;
                    listV.value = car.id;

                    toggleSection(true); // إظهار الأقسام
                    fillCarForm(car); // تعبئة البيانات
                    setBtnNewState(true);
                    resultV.style.display = 'none';
                }

            });

        searchHandler(searchC, resultC, clients,
            key => c => [c.name, c.phone, c.email].some(val => val.toLowerCase().includes(key)), {
                label: c => `${c.name} - ${c.phone}`,
                action: client => {
                    const userCars = cars.filter(car => car.client_id == client.id);
                    const countV = userCars.length;

                    if (countV > 1) {
                        listV.innerHTML = `<option value="0">${countV} Véhicule(s) trouvé</option>
                           <option value="0"> + Ajoutez</option>` +
                            userCars.map(car =>
                                `<option value="${car.id}">${car.marque} ${car.model} - ${car.immatriculation}</option>`
                            ).join('');
                        clearCarFields();
                    } else if (countV === 1) {
                        const car = userCars[0];
                        listV.innerHTML =
                            `<option value="${car.id}">${car.marque} ${car.model} - ${car.immatriculation}</option>`;
                        fillCarForm(car); // ⬅️ نعرض السيارة مباشرة
                    } else {
                        listV.innerHTML = `<option value="0">Aucune Véhicule trouvé</option>
                           <option value="0"> + Ajoutez</option>`;
                        clearCarFields();
                    }

                    fillClientForm(client);
                    toggleSection(true);
                    setBtnNewState(true);
                    resultC.style.display = 'none';
                }


            });
        const setBtnNewState = isOpen => {
            icon.classList.remove('mdi-plus', 'mdi-refresh');
            icon.classList.add(isOpen ? 'mdi-refresh' : 'mdi-plus');

            btnNew.classList.toggle('btn-success', !isOpen);
            btnNew.classList.toggle('btn-danger', isOpen);

            // ✅ تعديل النص حسب الحالة
            const iconText = document.getElementById('iconText');
            iconText.textContent = isOpen ? 'Réinitialiser' : 'Nouveau';
        };

        btnNew.onclick = () => {
            const isOpen = carInfo.style.display === 'block';
            toggleSection(!isOpen);
            clearFields();
            listV.innerHTML = '';
            searchV.value = '';
            searchC.value = '';
            client_id.value = '';
            setBtnNewState(!isOpen);
        };


        document.addEventListener('click', e => {
            if (!searchV.contains(e.target) && !resultV.contains(e.target)) resultV.style.display = 'none';
            if (!searchC.contains(e.target) && !resultC.contains(e.target)) resultC.style.display = 'none';
        });

        listV.addEventListener('change', e => {
            const selectedValue = e.target.value;
            toggleSection(true);

            if (selectedValue == 0) {
                // نفرغ فقط بيانات السيارة
                marque.selectedIndex = 0;
                selectModel.innerHTML = '<option value="">Choisissez...</option>';
                inputs[0].value = ''; // immatriculation
                inputs[1].value = ''; // n_chassis
                inputs[2].value = ''; // dpmc
                // لا نمسح بيانات العميل (inputs[3] إلى inputs[7])
            } else {
                const selectedCar = cars.find(c => c.id == selectedValue);
                if (selectedCar) {
                    fillCarForm(selectedCar);
                }
            }


        });
        const clearCarFields = () => {
            marque.selectedIndex = 0;
            selectModel.innerHTML = '<option value="">Choisissez...</option>';
            inputs[0].value = ''; // immatriculation
            inputs[1].value = ''; // n_chassis
            inputs[2].value = ''; // dpmc
        };
    </script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\new\resources\views\rdv\create.blade.php ENDPATH**/ ?>