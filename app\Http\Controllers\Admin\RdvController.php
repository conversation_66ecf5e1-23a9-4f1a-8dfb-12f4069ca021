<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PhotoAv;
use App\Models\PhotoAp;
use App\Models\File;
use Illuminate\Http\Request;
use App\Models\Rdv;
use App\Models\Car;
use App\Models\Client;
use App\Models\Marque;
use App\Models\Modele;

class RdvController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rdvs = Rdv::with('car')->get();
        return view('rdv.index', compact('rdvs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $clients = Client::all();
        $cars = Car::with('client')->get();
        $marques = Marque::all();
        $modeles = Modele::all();
        return view('rdv.create', compact('clients', 'cars', 'marques', 'modeles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Rdv $rdv)
    {
        $rdv->load('car', 'photoAv', 'photoAp', 'files');
        return view('rdv.show', compact('rdv'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    public function uploads(Request $request, string $id)
    {
        if (
            !$request->hasFile('photoAv') &&
            !$request->hasFile('photoAp') &&
            !$request->hasFile('files')
        ) {
            return redirect()->back()->with('error', 'Aucun fichier téléversé');
        }

        $rdv = Rdv::findOrFail($id);

        $rdv->status = 'Fixé';
        $rdv->save();



        // مصفوفة تحتوي على أنواع الملفات، والمجلد المخصص لها، والنموذج المرتبط بها
        $types = [
            'photoAv' => ['folder' => 'photoav', 'model' => PhotoAv::class, 'field' => 'image_path'],
            'photoAp' => ['folder' => 'photoap', 'model' => PhotoAp::class, 'field' => 'image_path'],
            'files'   => ['folder' => 'files',   'model' => File::class,    'field' => 'file_path'],
        ];

        foreach ($types as $inputName => $config) {
            if ($request->hasFile($inputName)) {
                foreach ($request->file($inputName) as $file) {
                    if ($file && $file->isValid()) {
                        $destinationPath = base_path("assets/uploads/{$rdv->id}/{$config['folder']}");

                        if (!file_exists($destinationPath)) {
                            mkdir($destinationPath, 0755, true);
                        }

                        $fileName = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();
                        $file->move($destinationPath, $fileName);

                        $model = new $config['model'];
                        $model->{$config['field']} = "assets/uploads/{$rdv->id}/{$config['folder']}/{$fileName}";
                        $model->rdv_id = $rdv->id;
                        $model->save();
                    }
                }
            }
        }

        return redirect()->route('rdv.show', $id)->with('success', 'Les fichiers ont été chargés avec succès.');
    }




    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
