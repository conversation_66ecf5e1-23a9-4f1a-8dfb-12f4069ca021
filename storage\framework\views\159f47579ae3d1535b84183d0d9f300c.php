<div class="topbar-custom">
    <div class="container-xxl">
        <div class="d-flex justify-content-between">
            <ul class="list-unstyled topnav-menu mb-0 d-flex align-items-center">
                <li>
                    <button class="button-toggle-menu nav-link ps-0">
                        <i data-feather="menu" class="noti-icon"></i>
                    </button>

                </li>

            </ul>


            <ul class="list-unstyled topnav-menu mb-0 d-flex align-items-center">

                <li class="d-none d-sm-flex">
                    <button type="button" class="btn nav-link" data-toggle="fullscreen">
                        <i data-feather="maximize" class="align-middle fullscreen noti-icon"></i>
                    </button>
                </li>

                <li class="d-none d-sm-flex">
                    <button type="button" class="btn nav-link" id="theme-toggle">
                        <span id="theme-icon">🌙</span>
                    </button>
                </li>

                <li class="dropdown notification-list topbar-dropdown">
                    <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button"
                        aria-haspopup="false" aria-expanded="false">
                        <i data-feather="bell" class="noti-icon"></i>
                        <span class="badge bg-danger rounded-circle noti-icon-badge">2</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-lg">

                        <!-- item-->
                        <div class="dropdown-item noti-title">
                            <h5 class="m-0">
                                <span class="float-end">
                                    <a href="" class="text-dark">
                                        <small>Nèttoyer tous</small>
                                    </a>
                                </span>Notifications
                            </h5>
                        </div>

                        <div class="noti-scroll" data-simplebar>

                            <!-- item-->
                            <a href="javascript:void(0);"
                                class="dropdown-item notify-item text-muted link-primary active">
                                <div class="notify-icon">
                                    <img src="<?php echo e(url('assets/images/users/user-12.jpg')); ?>"
                                        class="img-fluid rounded-circle" alt="" />
                                </div>
                                <div class="d-flex align-items-center justify-content-between">
                                    <p class="notify-details">Carl Steadham</p>
                                    <small class="text-muted">il y'a 5 min</small>
                                </div>
                                <p class="mb-0 user-msg">
                                    <small class="fs-14">Completed <span class="text-reset">Improve workflow in
                                            Figma</span></small>
                                </p>
                            </a>

                            <!-- item-->
                            <a href="javascript:void(0);" class="dropdown-item notify-item text-muted link-primary">
                                <div class="notify-icon">
                                    <img src="<?php echo e(url('assets/images/users/user-2.jpg')); ?>"
                                        class="img-fluid rounded-circle" alt="" />
                                </div>
                                <div class="notify-content">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <p class="notify-details">Olivia McGuire</p>
                                        <small class="text-muted">il y'a 8 min</small>
                                    </div>

                                    <div class="d-flex mt-2 align-items-center">
                                        <div class="notify-sub-icon">
                                            <i class="mdi mdi-download-box text-dark"></i>
                                        </div>

                                        <div>
                                            <p class="notify-details mb-0">dark-themes.zip</p>
                                            <small class="text-muted">2.4 MB</small>
                                        </div>
                                    </div>

                                </div>
                            </a>

                            <!-- item-->

                        </div>

                        <!-- All-->
                        <a href="javascript:void(0);"
                            class="dropdown-item text-center text-primary notify-item notify-all">
                            View all
                            <i class="fe-arrow-right"></i>
                        </a>

                    </div>
                </li>

                <li class="dropdown notification-list topbar-dropdown">
                    <?php
                        $nom = Auth::user()->name;
                        $parts = explode(' ', trim($nom));
                        $initials = '';

                        if (count($parts) >= 2) {
                            $initials = strtoupper(substr($parts[0], 0, 1) . substr($parts[1], 0, 1));
                        } elseif (count($parts) === 1) {
                            $initials = strtoupper(substr($parts[0], 0, 1));
                        }
                    ?>

                    <a class="nav-link dropdown-toggle nav-user me-0" data-bs-toggle="dropdown" href="#"
                        role="button" aria-haspopup="false" aria-expanded="false">
                        <span class="rounded-circle bg-primary p-1 text-white"><?php echo e($initials); ?></span>
                        <span class="pro-user-name ms-1">
                            <?php echo e(strtoupper($parts[0])); ?>

                            <i class="mdi mdi-chevron-down"></i>
                        </span>
                    </a>

                    <div class="dropdown-menu dropdown-menu-end profile-dropdown ">
                        <!-- item-->
                        <div class="dropdown-header noti-title">
                            <h6 class="text-overflow m-0">Bienvenu !</h6>
                        </div>

                        <!-- item-->
                        <a href="<?php echo e(route('dashboard')); ?>" class="dropdown-item notify-item">
                            <i class="mdi mdi-account-circle-outline fs-16 align-middle"></i>
                            <span>Mon Profil</span>
                        </a>

                        <!-- item-->
                        <a href="auth-lock-screen.html" class="dropdown-item notify-item">
                            <i class="mdi mdi-lock-outline fs-16 align-middle"></i>
                            <span>Lock Screen</span>
                        </a>

                        <div class="dropdown-divider"></div>

                        <!-- item-->
                        <a href="<?php echo e(route('auth.logout')); ?>" class="dropdown-item notify-item">
                            <i class="mdi mdi-location-exit fs-16 align-middle"></i>
                            <span>Déconnecter</span>
                        </a>

                    </div>
                </li>

            </ul>
        </div>

    </div>

</div>
<script>
// Dark Mode Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing theme toggle...');

    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.getElementById('theme-icon');
    const body = document.body;

    console.log('Elements found:', {
        themeToggle: !!themeToggle,
        themeIcon: !!themeIcon
    });

    if (!themeToggle || !themeIcon) {
        console.error('Theme toggle elements not found');
        return;
    }

    // Check system preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    console.log('System prefers dark:', prefersDark);

    // Get saved theme or use system preference
    const savedTheme = localStorage.getItem('theme') || (prefersDark ? 'dark' : 'light');
    console.log('Saved theme:', savedTheme);

    // Apply initial theme
    function applyTheme(theme) {
        console.log('Applying theme:', theme);
        if (theme === 'dark') {
            body.setAttribute('data-theme', 'dark');
            themeIcon.textContent = '☀️';
        } else {
            body.removeAttribute('data-theme');
            themeIcon.textContent = '🌙';
        }
        console.log('Theme applied. Body data-theme:', body.getAttribute('data-theme'));
        console.log('Icon text:', themeIcon.textContent);
    }

    applyTheme(savedTheme);

    // Toggle theme function
    themeToggle.addEventListener('click', function() {
        console.log('Theme toggle clicked');
        const currentTheme = body.getAttribute('data-theme');
        console.log('Current theme:', currentTheme);

        if (currentTheme === 'dark') {
            applyTheme('light');
            localStorage.setItem('theme', 'light');
        } else {
            applyTheme('dark');
            localStorage.setItem('theme', 'dark');
        }
    });

    console.log('Theme toggle initialized successfully');
});
</script>

<?php /**PATH C:\xampp\htdocs\new\resources\views/inc/nav.blade.php ENDPATH**/ ?>