<div class="topbar-custom modern-topbar">
    <div class="container-xxl">
        <div class="d-flex justify-content-between align-items-center">
            <div class="topbar-left d-flex align-items-center">
                <button class="button-toggle-menu nav-link modern-menu-toggle">
                    <i data-feather="menu" class="noti-icon"></i>
                </button>

                <!-- Search Bar -->
                <div class="search-container d-none d-md-flex">
                    <div class="search-input-wrapper">
                        <i data-feather="search" class="search-icon"></i>
                        <input type="text" class="search-input" placeholder="Rechercher...">
                    </div>
                </div>
            </div>

            <div class="topbar-right d-flex align-items-center gap-3">

                <!-- Theme Toggle -->
                <button type="button" class="theme-toggle-btn d-none d-sm-flex" onclick="toggleTheme()">
                    <i data-feather="sun" class="theme-icon light-icon"></i>
                    <i data-feather="moon" class="theme-icon dark-icon d-none"></i>
                </button>

                <!-- Fullscreen Toggle -->
                <button type="button" class="fullscreen-btn d-none d-sm-flex" data-toggle="fullscreen">
                    <i data-feather="maximize" class="align-middle fullscreen noti-icon"></i>
                </button>

                <!-- Notifications -->
                <div class="dropdown notification-dropdown">
                    <button class="notification-btn" data-bs-toggle="dropdown" aria-expanded="false">
                        <i data-feather="bell" class="noti-icon"></i>
                        <span class="notification-badge">2</span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end notifications-dropdown">
                        <!-- Notification Header -->
                        <div class="notification-header">
                            <h5 class="notification-title">Notifications</h5>
                            <a href="#" class="notification-clear-btn">Tout effacer</a>
                        </div>

                        <!-- Notification List -->
                        <div class="notification-list" data-simplebar>

                            <!-- Notification Item -->
                            <a href="javascript:void(0);" class="notification-item active">
                                <div class="notification-avatar">
                                    <img src="<?php echo e(url('assets/images/users/user-12.jpg')); ?>" alt="User Avatar" />
                                </div>
                                <div class="notification-content">
                                    <div class="notification-header-info">
                                        <p class="notification-user">Carl Steadham</p>
                                        <small class="notification-time">il y'a 5 min</small>
                                    </div>
                                    <p class="notification-message">
                                        Completed <span class="text-reset">Improve workflow in Figma</span>
                                    </p>
                                </div>
                            </a>

                            <!-- Notification Item -->
                            <a href="javascript:void(0);" class="notification-item">
                                <div class="notification-avatar">
                                    <img src="<?php echo e(url('assets/images/users/user-2.jpg')); ?>" alt="User Avatar" />
                                </div>
                                <div class="notification-content">
                                    <div class="notification-header-info">
                                        <p class="notification-user">Olivia McGuire</p>
                                        <small class="notification-time">il y'a 8 min</small>
                                    </div>
                                    <div class="notification-file">
                                        <div class="file-icon">
                                            <i class="mdi mdi-download-box"></i>
                                        </div>
                                        <div class="file-info">
                                            <p class="file-name">dark-themes.zip</p>
                                            <small class="file-size">2.4 MB</small>
                                        </div>
                                    </div>
                                </div>
                            </a>

                        </div>

                        <!-- View All -->
                        <div class="notification-footer">
                            <a href="javascript:void(0);" class="view-all-btn">
                                Voir toutes les notifications
                                <i data-feather="arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- User Profile Dropdown -->
                <div class="dropdown user-dropdown">
                    <?php
                        $nom = Auth::user()->name;
                        $parts = explode(' ', trim($nom));
                        $initials = '';

                        if (count($parts) >= 2) {
                            $initials = strtoupper(substr($parts[0], 0, 1) . substr($parts[1], 0, 1));
                        } elseif (count($parts) === 1) {
                            $initials = strtoupper(substr($parts[0], 0, 1));
                        }
                    ?>

                    <button class="user-profile-btn" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="user-avatar">
                            <span class="avatar-initials"><?php echo e($initials); ?></span>
                        </div>
                        <div class="user-info d-none d-md-block">
                            <span class="user-name"><?php echo e($parts[0]); ?></span>
                            <i data-feather="chevron-down" class="dropdown-icon"></i>
                        </div>
                    </button>

                    <div class="dropdown-menu dropdown-menu-end user-dropdown-menu">
                        <!-- Profile Header -->
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <span class="avatar-initials"><?php echo e($initials); ?></span>
                            </div>
                            <div class="profile-info">
                                <h6 class="profile-name"><?php echo e(Auth::user()->name); ?></h6>
                                <p class="profile-email"><?php echo e(Auth::user()->email); ?></p>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>

                        <!-- Profile Menu Items -->
                        <a href="<?php echo e(route('dashboard')); ?>" class="profile-menu-item">
                            <i data-feather="user" class="menu-icon"></i>
                            <span>Mon Profil</span>
                        </a>

                        <a href="#" class="profile-menu-item">
                            <i data-feather="settings" class="menu-icon"></i>
                            <span>Paramètres</span>
                        </a>

                        <a href="#" class="profile-menu-item">
                            <i data-feather="help-circle" class="menu-icon"></i>
                            <span>Aide</span>
                        </a>

                        <div class="dropdown-divider"></div>

                        <a href="<?php echo e(route('auth.logout')); ?>" class="profile-menu-item logout-item">
                            <i data-feather="log-out" class="menu-icon"></i>
                            <span>Déconnexion</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Topbar Styles */
.modern-topbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--neutral-200);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.modern-menu-toggle {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
}

.modern-menu-toggle:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
}

/* Search Bar Styles */
.search-container {
    margin-left: var(--spacing-lg);
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--secondary-400);
    width: 18px;
    height: 18px;
    z-index: 1;
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-xl);
    background: var(--neutral-50);
    color: var(--secondary-700);
    font-size: var(--font-size-sm);
    width: 300px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    background: white;
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* Theme Toggle Button */
.theme-toggle-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
    position: relative;
}

.theme-toggle-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
}

/* Fullscreen Button */
.fullscreen-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    transition: all var(--transition-fast);
}

.fullscreen-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
}

/* Notification Button */
.notification-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--secondary-600);
    position: relative;
    transition: all var(--transition-fast);
}

.notification-btn:hover {
    background: var(--neutral-100);
    color: var(--primary-600);
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--danger-500);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Notifications Dropdown */
.notifications-dropdown {
    width: 380px;
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: var(--spacing-sm);
}

.notification-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--neutral-200);
    display: flex;
    justify-content: between;
    align-items: center;
}

.notification-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--secondary-800);
    margin: 0;
}

.notification-clear-btn {
    color: var(--primary-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.notification-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    padding: var(--spacing-lg);
    text-decoration: none;
    color: inherit;
    border-bottom: 1px solid var(--neutral-100);
    transition: all var(--transition-fast);
}

.notification-item:hover {
    background: var(--neutral-50);
}

.notification-item.active {
    background: var(--primary-50);
    border-left: 3px solid var(--primary-500);
}

.notification-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.notification-content {
    margin-left: var(--spacing-md);
    flex: 1;
}

.notification-header-info {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.notification-user {
    font-weight: 500;
    color: var(--secondary-800);
    margin: 0;
}

.notification-time {
    color: var(--secondary-500);
    font-size: var(--font-size-xs);
}

.notification-message {
    color: var(--secondary-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.notification-file {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xs);
}

.file-icon {
    color: var(--primary-600);
}

.file-name {
    font-weight: 500;
    color: var(--secondary-800);
    margin: 0;
}

.file-size {
    color: var(--secondary-500);
    font-size: var(--font-size-xs);
}

.notification-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--neutral-200);
    text-align: center;
}

.view-all-btn {
    color: var(--primary-600);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* User Profile Button */
.user-profile-btn {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.user-profile-btn:hover {
    background: var(--neutral-100);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.user-name {
    font-weight: 500;
    color: var(--secondary-800);
}

.dropdown-icon {
    width: 16px;
    height: 16px;
    color: var(--secondary-500);
}

/* User Dropdown Menu */
.user-dropdown-menu {
    width: 280px;
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0;
    margin-top: var(--spacing-sm);
}

.profile-header {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.profile-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.profile-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
}

.profile-email {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    text-decoration: none;
    color: var(--secondary-700);
    transition: all var(--transition-fast);
}

.profile-menu-item:hover {
    background: var(--neutral-50);
    color: var(--primary-600);
}

.profile-menu-item.logout-item {
    color: var(--danger-600);
}

.profile-menu-item.logout-item:hover {
    background: var(--danger-50);
    color: var(--danger-700);
}

.menu-icon {
    width: 18px;
    height: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-container {
        display: none !important;
    }

    .user-info {
        display: none !important;
    }

    .theme-toggle-btn,
    .fullscreen-btn {
        display: none !important;
    }

    .topbar-right {
        gap: var(--spacing-sm) !important;
    }
}
</style>

<script>
// Theme Toggle Functionality
function toggleTheme() {
    const body = document.body;
    const lightIcon = document.querySelector('.light-icon');
    const darkIcon = document.querySelector('.dark-icon');

    if (body.getAttribute('data-theme') === 'dark') {
        body.removeAttribute('data-theme');
        lightIcon.classList.remove('d-none');
        darkIcon.classList.add('d-none');
        localStorage.setItem('theme', 'light');
    } else {
        body.setAttribute('data-theme', 'dark');
        lightIcon.classList.add('d-none');
        darkIcon.classList.remove('d-none');
        localStorage.setItem('theme', 'dark');
    }
}

// Load saved theme
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    const lightIcon = document.querySelector('.light-icon');
    const darkIcon = document.querySelector('.dark-icon');

    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        lightIcon.classList.add('d-none');
        darkIcon.classList.remove('d-none');
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\new\resources\views/inc/nav.blade.php ENDPATH**/ ?>